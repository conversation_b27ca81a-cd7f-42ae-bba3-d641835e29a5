# Git Workflow and Troubleshooting Guide

## Overview

This document provides guidance for common Git workflows and troubleshooting scenarios encountered in the OracleToPostgreSQL project.

## Common Git Issues and Solutions

### 1. Branch Divergence - Push Rejected (Non-Fast-Forward)

#### Problem
```bash
git push origin main
# Output:
# ! [rejected]        main -> main (non-fast-forward)
# error: failed to push some refs to 'http://************:3000/CBIT2024/OracleToPostgreSQL.git'
# hint: Updates were rejected because the tip of your current branch is behind
# hint: its remote counterpart.
```

#### Diagnosis Steps

1. **Check current status:**
```bash
git status
```
Expected output when branches have diverged:
```
On branch main
Your branch and 'origin/main' have diverged,
and have 1 and 1 different commits each, respectively.
```

2. **Compare local and remote commit history:**
```bash
# Check local commits
git log --oneline -5

# Check remote commits
git log --oneline origin/main -5
```

3. **Identify the divergence point:**
Look for the last common commit between local and remote branches.

#### Solution: Merge Strategy

**Step 1: Fetch latest remote changes**
```bash
git fetch origin
```

**Step 2: Test merge for conflicts**
```bash
git merge origin/main --no-commit --no-ff
```

**Step 3: Check merge status**
```bash
git status
```

If merge is successful (no conflicts):
```
All conflicts fixed but you are still merging.
  (use "git commit" to conclude merge)
```

**Step 4: Complete the merge**
```bash
git commit -m "Merge remote-tracking branch 'origin/main'

Merging V.X.X.X remote changes with V.Y.Y.Y local changes.
- Brief description of remote changes
- Brief description of local changes
- Any important notes about the merge"
```

**Step 5: Push merged changes**
```bash
git push origin main
```

#### Alternative Solution: Rebase Strategy

If you prefer a linear history without merge commits:

```bash
# Rebase local commits on top of remote
git rebase origin/main

# If conflicts occur, resolve them and continue
git add <resolved-files>
git rebase --continue

# Push the rebased changes
git push origin main
```

### 2. Checking What Changes Will Be Merged

Before merging, you can preview what changes will be included:

```bash
# See files that will be affected
git diff --name-only HEAD origin/main

# See detailed changes
git diff HEAD origin/main

# See commits that will be merged
git log HEAD..origin/main --oneline
```

### 3. Handling Merge Conflicts

If the merge has conflicts:

```bash
# Check which files have conflicts
git status

# Edit conflicted files to resolve conflicts
# Look for conflict markers: <<<<<<<, =======, >>>>>>>

# After resolving conflicts, stage the files
git add <resolved-files>

# Complete the merge
git commit
```

### 4. Detached HEAD State - Reset Main Branch to Current HEAD

#### Problem
```bash
git status
# Output:
# HEAD detached from 6d9185b
# nothing to commit, working tree clean
```

This occurs when you've made commits while not on any branch, often after checking out a specific commit.

#### Diagnosis Steps

1. **Check current HEAD and branch status:**
```bash
git status
git log --oneline -3
git branch -a
```

2. **Identify target commit and current branch positions:**
```bash
# Get current HEAD commit
git rev-parse HEAD

# Get main branch commit
git rev-parse main

# Check remote status
git log --oneline origin/main -3
```

#### Solution: Reset Main Branch to Current HEAD

**Step 1: Create Safety Backup (CRITICAL)**
```bash
# Create backup tag at current HEAD
git tag backup-before-reset HEAD

# Alternative: Create bundle backup
git bundle create backup-$(date +%Y%m%d-%H%M%S).bundle HEAD
```

**Step 2: Switch to Main Branch**
```bash
git checkout main
```
Expected warning about leaving commits behind - this is normal.

**Step 3: Reset Main Branch to Target Commit**
```bash
# Reset main to point to the desired commit (replace with actual commit hash)
git reset --hard 102b47c
```

**Step 4: Verify Operation**
```bash
# Check status
git status

# Verify commits match
git log --oneline -3

# Confirm HEAD and main point to same commit
echo "HEAD: $(git rev-parse HEAD)"
echo "main: $(git rev-parse main)"
```

**Step 5: Synchronize with Remote**
```bash
# Fetch latest remote state
git fetch origin

# Check relationship with remote
git status

# Force push with safety check (USE WITH CAUTION)
git push --force-with-lease origin main
```

#### Complete Command Sequence for Detached HEAD Reset

```bash
# 1. Create safety backup
git tag backup-before-reset HEAD
echo "✅ Backup created at tag: backup-before-reset"

# 2. Switch to main branch
git checkout main

# 3. Reset main to current HEAD commit (replace with actual hash)
git reset --hard 102b47c

# 4. Verify operation
echo "=== Verification ==="
git status
git log --oneline -3

# 5. Synchronize with remote
git fetch origin
git push --force-with-lease origin main

# 6. Final verification
git status
echo "✅ Operation completed successfully"
```

#### Recovery from Backup

If something goes wrong:
```bash
# List available backup tags
git tag | grep backup

# Restore from backup
git reset --hard backup-before-reset

# Or check reflog for recent operations
git reflog
```

### 5. Undoing a Merge (If Needed)

If you need to undo a merge before pushing:

```bash
# Undo the merge (before committing)
git merge --abort

# Undo the merge (after committing but before pushing)
git reset --hard HEAD~1
```

## Best Practices

### 1. Regular Synchronization

```bash
# Fetch remote changes regularly
git fetch origin

# Check if your branch is behind
git status
```

### 2. Before Starting New Work

```bash
# Always start with the latest remote changes
git pull origin main
```

### 3. Before Pushing

```bash
# Check status
git status

# Ensure you're up to date
git fetch origin
git status

# If behind, merge or rebase first
git pull origin main  # or git merge origin/main
```

### 4. Commit Message Best Practices

```bash
git commit -m "V.X.X.X feat: brief description

Detailed explanation of changes:
- Specific change 1
- Specific change 2
- Any breaking changes or important notes"
```

## Project-Specific Workflow

### Version Numbering

This project uses semantic versioning in commit messages:
- `V.X.X.X feat:` - New features
- `V.X.X.X fix:` - Bug fixes
- `V.X.X.X refactor:` - Code refactoring
- `V.X.X.X docs:` - Documentation updates

### Common Commands for This Project

```bash
# Build and test before committing
make build
make test

# Run the application to verify changes
make run ARGS="-s -d -p=TEST"

# Check git status
git status

# Add changes
git add .

# Commit with version number
git commit -m "V.X.X.X feat: description of changes"

# Push to remote
git push origin main
```

### Real-World Scenario: Detached HEAD Recovery

**Scenario**: After working on commits, you find yourself in detached HEAD state with important changes that need to become the new main branch.

**Example from V.0.0.15 Recovery**:
```bash
# Initial state: HEAD detached at commit 102b47c (V.0.0.15)
# main branch pointing to commit 4bbf287 (V.0.0.14)
# Goal: Make main point to V.0.0.15 commit

# 1. Create backup
git tag backup-before-reset HEAD

# 2. Switch to main and reset
git checkout main
git reset --hard 102b47c

# 3. Force push to update remote
git push --force-with-lease origin main

# Result: main branch now points to V.0.0.15, detached HEAD resolved
```

**When to use this approach**:
- ✅ You have important commits in detached HEAD state
- ✅ You want these commits to become the new main branch
- ✅ You're willing to rewrite remote history (coordinate with team)
- ✅ You've created proper backups

## Troubleshooting Checklist

When Git operations fail:

1. ✅ Check `git status`
2. ✅ Check `git log --oneline -5`
3. ✅ Check `git log --oneline origin/main -5`
4. ✅ Run `git fetch origin`
5. ✅ Compare local and remote with `git status`
6. ✅ **If in detached HEAD state**: Follow detached HEAD reset procedure
7. ✅ **If branches diverged**: Choose merge or rebase strategy
8. ✅ Test merge with `--no-commit` flag first
9. ✅ Complete merge and push

### Common Git States and Solutions

| Git Status Message | Issue | Solution Section |
|-------------------|-------|------------------|
| `HEAD detached from <commit>` | Detached HEAD state | [Detached HEAD Reset](#4-detached-head-state---reset-main-branch-to-current-head) |
| `Your branch and 'origin/main' have diverged` | Branch divergence | [Branch Divergence](#1-branch-divergence---push-rejected-non-fast-forward) |
| `! [rejected] main -> main (non-fast-forward)` | Push rejected | [Branch Divergence](#1-branch-divergence---push-rejected-non-fast-forward) |
| `nothing to commit, working tree clean` | Normal state | No action needed |

## Emergency Recovery

### If You Accidentally Force Push

```bash
# Check reflog to find previous commit
git reflog

# Reset to previous state
git reset --hard <previous-commit-hash>

# Coordinate with team to restore remote if needed
```

### If Remote Repository is Corrupted

```bash
# Create backup of local changes
git bundle create backup.bundle HEAD

# Contact repository administrator
# Restore from backup if necessary
```

## Repository Information

- **Remote URL**: `http://************:3000/CBIT2024/OracleToPostgreSQL.git`
- **Default Branch**: `main`
- **Git User**: `<EMAIL>`

## Related Documentation

- [Usage Guide](usage/README.md) - Application usage and Make commands
- [Configuration Guide](README-Configuration.md) - Database configuration
- [Troubleshooting Guide](troubleshooting.md) - Application troubleshooting
