# Maintenance Guide

## Package Management

### Upgrading NuGet Packages

To keep the project up-to-date with the latest package versions, follow these steps:

#### 1. Check for Outdated Packages

```bash
dotnet list package --outdated
```

This command will show all packages that have newer versions available, displaying:
- Current requested version
- Currently resolved version  
- Latest available version

#### 2. Update Individual Packages

Update packages one by one to their latest versions:

```bash
# Update Dapper
dotnet add package Dapper --version [LATEST_VERSION]

# Update Devart.Data
dotnet add package Devart.Data --version [LATEST_VERSION]

# Update Devart.Data.Oracle
dotnet add package Devart.Data.Oracle --version [LATEST_VERSION]

# Update Devart.Data.PostgreSQL
dotnet add package Devart.Data.PostgreSQL --version [LATEST_VERSION]
```

#### 3. Verify Package Updates

After updating packages, verify the changes in the project file:

```bash
cat OracleToPostgresConverterProject.csproj
```

#### 4. Test the Build

Always test the build after package updates:

```bash
dotnet build
# or
make build
```

### Recent Package Updates (July 2025)

The following packages were updated to their latest versions:

| Package | Previous Version | Updated Version | Update Date |
|---------|------------------|-----------------|-------------|
| Dapper | 2.1.35 | 2.1.66 | 2025-07-18 |
| Devart.Data | 6.0.21 | 6.0.235 | 2025-07-18 |
| Devart.Data.Oracle | 10.3.21 | 10.4.235 | 2025-07-18 |
| Devart.Data.PostgreSQL | 8.3.21 | 8.4.235 | 2025-07-18 |

## Build Artifact Cleanup

### Removing Old Framework Build Files

When upgrading .NET framework versions or cleaning up old build artifacts:

#### 1. Identify Old Build Directories

Check for outdated framework build directories:

```bash
ls -la bin/Debug/
ls -la bin/Release/
```

#### 2. Remove Old Framework Files

Remove specific framework version directories (e.g., net6.0 when using net9.0):

```bash
# Remove .NET 6.0 debug artifacts
rm -rf bin/Debug/net6.0

# Remove .NET 6.0 release artifacts (if present)
rm -rf bin/Release/net6.0
```

#### 3. Clean All Build Artifacts

For a complete clean build:

```bash
# Clean all build outputs
dotnet clean

# Or using make
make clean
```

#### 4. Rebuild Project

After cleanup, rebuild to ensure everything works:

```bash
dotnet build
# or
make build
```

### Automatic Runtime Directory Cleanup

#### Problem: Unwanted Runtime Directories

Transitive dependencies from NuGet packages (like Devart packages) can create unwanted runtime directories:
- `bin/Debug/net9.0/runtimes/win/lib/netcoreapp2.0/`
- `bin/Debug/net9.0/runtimes/win/lib/netstandard2.0/`

These directories contain older framework libraries that may not be needed for your .NET 9 application.

#### Solution: MSBuild Target

The project includes an MSBuild target that automatically removes these directories after each build:

```xml
<!-- MSBuild target to remove unwanted runtime folders after build -->
<Target Name="RemoveOldRuntimeFolders" AfterTargets="Build">
  <ItemGroup>
    <OldRuntimeFolders Include="$(OutputPath)runtimes/win/lib/netcoreapp2.0/**" />
    <OldRuntimeFolders Include="$(OutputPath)runtimes/win/lib/netstandard2.0/**" />
  </ItemGroup>
  <Delete Files="@(OldRuntimeFolders)" />
  <RemoveDir Directories="$(OutputPath)runtimes/win/lib/netcoreapp2.0;$(OutputPath)runtimes/win/lib/netstandard2.0" Condition="Exists('$(OutputPath)runtimes/win/lib/netcoreapp2.0') Or Exists('$(OutputPath)runtimes/win/lib/netstandard2.0')" />
</Target>
```

#### How It Works

1. **Automatic**: Runs after every build (Debug and Release)
2. **Targeted**: Only removes specific unwanted directories
3. **Safe**: Doesn't affect application functionality
4. **Maintenance-free**: No manual intervention required

#### Verification

After building, check that the runtime directories are clean:

```bash
# Should be empty or not exist
ls -la bin/Debug/net9.0/runtimes/win/lib/
ls -la bin/Release/net9.0/runtimes/win/lib/
```

## Best Practices

### Regular Maintenance Schedule

1. **Monthly**: Check for package updates using `dotnet list package --outdated`
2. **Before Major Releases**: Update all packages to latest stable versions
3. **After Framework Updates**: Clean old framework build artifacts
4. **After Updates**: Always run full build and test suite

### Version Management

- Keep track of package versions in this documentation
- Test thoroughly after major version updates
- Consider security updates as high priority
- Document any breaking changes or compatibility issues

### Build Environment

- Ensure consistent .NET SDK version across development environments
- Use `make build` for consistent release builds
- Clean build artifacts when switching between debug/release configurations

## Troubleshooting

### Common Issues After Package Updates

1. **Build Failures**: Check for breaking changes in package release notes
2. **Runtime Errors**: Verify all dependencies are compatible with the new versions
3. **Performance Issues**: Monitor application performance after major updates

### Recovery Steps

If issues occur after package updates:

1. Check git history for previous working versions
2. Revert to previous package versions if needed
3. Update packages incrementally rather than all at once
4. Test each update individually

## References

- [.NET CLI Package Management](https://docs.microsoft.com/en-us/dotnet/core/tools/dotnet-add-package)
- [NuGet Package Versioning](https://docs.microsoft.com/en-us/nuget/concepts/package-versioning)
- [Devart Data Providers Documentation](https://www.devart.com/dotconnect/)
