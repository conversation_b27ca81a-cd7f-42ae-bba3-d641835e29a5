# OracleToPostgres Converter - Usage Guide

## Command Line Interface

The OracleToPostgres converter is a command-line tool that supports various arguments to control its behavior.

### Basic Command Format

You can run the application using either `dotnet run` directly or using the provided Makefile:

**Using dotnet directly:**
```bash
dotnet run -- [options]
```

**Using Make (recommended):**
```bash
make run ARGS="[options]"
```

Both methods are equivalent and will produce the same results.

### Command Options

| Option | Long Form | Description |
|--------|-----------|-------------|
| `-s` | `--schema` | Transfer schema (tables, indexes, etc.) |
| `-d` | `--data` | Transfer data from Oracle to PostgreSQL |
| `-p=PREFIX` | `--prefix=PREFIX` | Specify table prefix(es) to include in transfer |
| `-e=TABLE1,TABLE2` | `--exclude=TABLE1,TABLE2` | Exclude specific tables from transfer |
| `-i` | `--ignore-matching` | Ignore tables with matching row counts |

### Option Details

#### Schema Transfer (`-s`, `--schema`)
When specified, the tool will create PostgreSQL tables based on Oracle table definitions. This includes:
- Table structure
- Primary keys
- Data types (converted appropriately)

#### Data Transfer (`-d`, `--data`)
When specified, the tool will transfer data from Oracle tables to PostgreSQL tables. The tool compares last update times and row counts to determine if a table needs to be transferred.

#### Table Prefixes (`-p=`, `--prefix=`)
Specifies which tables to include based on their name prefix. Multiple prefixes can be specified:
```bash
# Using Make
make run ARGS="-s -d -p=PREFIX1 -p=PREFIX2"

# Using dotnet directly
dotnet run -- -s -d -p=PREFIX1 -p=PREFIX2
```

To process all tables, use an empty prefix:
```bash
# Using Make
make run ARGS="-s -d -p="

# Using dotnet directly
dotnet run -- -s -d -p=
```

#### Exclude Tables (`-e=`, `--exclude=`)
Specifies which tables to exclude from processing, even if they match a prefix:
```bash
# Using Make
make run ARGS="-s -d -p=PREFIX -e=TABLE1,TABLE2"

# Using dotnet directly
dotnet run -- -s -d -p=PREFIX -e=TABLE1,TABLE2
```

#### Ignore Matching Row Counts (`-i`, `--ignore-matching`)
By default, tables with matching row counts in Oracle and PostgreSQL are skipped. This option forces processing even if row counts match.

## Usage Examples

### Using Make Commands

The following examples show how to use the Make commands for common scenarios:

#### 1. Schema and Data Transfer for Specific Prefix
```bash
make run ARGS="-s -d -p=CDISC"
```
Equivalent to: `dotnet run -- -s -d -p=CDISC`

#### 2. Schema and Data Transfer for Multiple Prefixes
```bash
make run ARGS="-s -d -p=SYSMMROLE -p=ANOTHER_PREFIX"
```
Equivalent to: `dotnet run -- -s -d -p=SYSMMROLE -p=ANOTHER_PREFIX`

#### 3. Schema Only for Specific Prefix
```bash
make run ARGS="-s -p=USER_"
```
Equivalent to: `dotnet run -- -s -p=USER_`

#### 4. Data Only for Multiple Prefixes
```bash
make run ARGS="-d -p=EMPLOYEE -p=DEPARTMENT"
```
Equivalent to: `dotnet run -- -d -p=EMPLOYEE -p=DEPARTMENT`

#### 5. Schema and Data for All Tables
```bash
make run ARGS="-s -d -p="
```
Equivalent to: `dotnet run -- -s -d -p=`

#### 6. Schema and Data for All Tables, Ignoring Matching Row Counts
```bash
make run ARGS="-s -d -p= -i"
```
Equivalent to: `dotnet run -- -s -d -p= -i`

#### 7. Schema and Data for All Tables Except Specific Ones
```bash
make run ARGS="-s -d -p= -e=TABLE1,TABLE2"
```
Equivalent to: `dotnet run -- -s -d -p= -e=TABLE1,TABLE2`

### Using dotnet run Directly

If you prefer to use `dotnet run` directly, here are the equivalent commands:

```bash
# Schema and data transfer for specific prefix
dotnet run -- -s -d -p=CDISC

# Schema and data transfer for multiple prefixes
dotnet run -- -s -d -p=SYSMMROLE -p=ANOTHER_PREFIX

# Schema only for specific prefix
dotnet run -- -s -p=USER_

# Data only for multiple prefixes
dotnet run -- -d -p=EMPLOYEE -p=DEPARTMENT

# Schema and data for all tables
dotnet run -- -s -d -p=

# Schema and data for all tables, ignoring matching row counts
dotnet run -- -s -d -p= -i

# Schema and data for all tables except specific ones
dotnet run -- -s -d -p= -e=TABLE1,TABLE2
```

## Make Command Features

The Makefile provides additional convenience features:

### Available Make Targets

- `make run` - Run the application (with or without arguments)
- `make run ARGS="..."` - Run with specific arguments
- `make build` - Build the project
- `make clean` - Clean build outputs
- `make test` - Run tests
- `make restore` - Restore NuGet packages
- `make publish` - Publish the application
- `make help` - Show all available commands

### Running Without Arguments

You can run the application without arguments to see the usage help:

```bash
make run
```

This will display the usage examples and available options.

### Build Integration

The Make commands automatically build the project before running, ensuring you're always running the latest version:

```bash
# This will build first, then run
make run ARGS="-s -d -p=CDISC"
```

## Process Flow

1. The tool connects to both Oracle and PostgreSQL databases
2. It identifies tables matching the specified prefixes (excluding any specified tables)
3. For each table:
   - If schema transfer is requested (`-s`), the table structure is created in PostgreSQL
   - If data transfer is requested (`-d`), data is transferred from Oracle to PostgreSQL
4. A log entry is maintained for each table in the `transfer_log` table

## Logging

The tool maintains a log table (`transfer_log`) in PostgreSQL with the following information:
- Table name
- Last Oracle update time
- Last transfer time
- Oracle row count
- PostgreSQL row count
- Processing time
- Status information

This log is used to track progress and determine which tables need to be updated in subsequent runs.
