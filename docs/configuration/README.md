# OracleToPostgres Converter - Configuration Guide

## Database Connection Strings

The OracleToPostgres converter requires properly formatted connection strings for both the source Oracle database and the target PostgreSQL database.

### Oracle Connection String

The Oracle connection string follows this format:

```
Data Source=[host];Service Name=[service_name];Direct=true; User ID=[username]; Password=[password];License Key=[license_key]
```

#### Parameters:

- `Data Source`: The Oracle server host (IP address or hostname)
- `Service Name`: The Oracle service name
- `User ID`: Oracle database username
- `Password`: Oracle database password
- `License Key`: Devart.Data.Oracle license key

### PostgreSQL Connection String

The PostgreSQL connection string follows this format:

```
Host=[host];Port=[port];Database=[database];User Id=[username];Password=[password];License Key=[license_key]
```

#### Parameters:

- `Host`: PostgreSQL server hostname or IP address
- `Port`: PostgreSQL server port (typically 5432)
- `Database`: Target database name in PostgreSQL
- `User Id`: PostgreSQL username
- `Password`: PostgreSQL password
- `License Key`: Devart.Data.PostgreSQL license key

## Configuration in Code

The connection strings are currently hardcoded in the `OracleToPostgresConverter.cs` file. For production use, it's recommended to:

1. Move these connection strings to a configuration file (e.g., appsettings.json)
2. Use environment variables for sensitive information
3. Consider using a secure secret management solution

Example implementation using appsettings.json:

```json
{
  "ConnectionStrings": {
    "Oracle": "Data Source=************;Service Name=ORCLPDB;Direct=true; User ID=username; Password=password;License Key=your_license_key",
    "PostgreSQL": "Host=************;Port=5432;Database=clinical_db;User Id=username;Password=password;License Key=your_license_key"
  }
}
```

## Table Transfer Configuration

### Included and Excluded Tables

You can control which tables are transferred using:

1. Table prefixes to include (`-p=` or `--prefix=`)
2. Specific tables to exclude (`-e=` or `--exclude=`)

### Data Transfer Behavior

Data transfer behavior can be controlled with:

- `-i` or `--ignore-matching`: Process tables even if row counts match between source and target

## Advanced Configuration

### Performance Tuning

The tool provides methods for different transfer approaches:

- `TransferBulk`: Faster for large tables
- `TransferRowByRow`: More reliable for complex data
- `ProcessDataDirectly`: Custom processing for specific data types
- `TransferLargeTable`: Handles tables with many rows by breaking them into chunks

These methods are selected automatically based on table characteristics but may require manual override for specific tables.
