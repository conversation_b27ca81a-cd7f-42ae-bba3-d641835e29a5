# Database Configuration Guide

## Overview
The Oracle to PostgreSQL converter now uses external configuration files instead of hardcoded connection strings, improving security and maintainability.

## Configuration Files

### `appsettings.json` (Production/Default)
Contains the main production database connection strings:
```json
{
  "ConnectionStrings": {
    "Oracle": "Your Oracle connection string",
    "PostgreSQL": "Your PostgreSQL connection string"
  }
}
```

### `appsettings.Development.json` (Development Environment)
Contains development database connection strings for local testing.

### `appsettings.Production.json` (Production Template)
Template for production deployment with placeholder values.

## Environment-Specific Configuration

The application will automatically load configuration files in this order:
1. `appsettings.json` (base configuration)
2. `appsettings.{Environment}.json` (environment-specific overrides)

To use different environments, set the `DOTNET_ENVIRONMENT` environment variable:
```bash
# For development
export DOTNET_ENVIRONMENT=Development
dotnet run

# For production
export DOTNET_ENVIRONMENT=Production
dotnet run
```

## Security Best Practices

### 1. Protect Configuration Files
- Add sensitive config files to `.gitignore`
- Use environment variables for production passwords
- Consider using Azure Key Vault or similar for production secrets

### 2. Environment Variables Override
You can override connection strings using environment variables:
```bash
export ConnectionStrings__Oracle="your_oracle_connection_string"
export ConnectionStrings__PostgreSQL="your_postgresql_connection_string"
```

### 3. User Secrets (Development)
For development, use .NET User Secrets to store sensitive data:
```bash
dotnet user-secrets init
dotnet user-secrets set "ConnectionStrings:Oracle" "your_dev_oracle_string"
dotnet user-secrets set "ConnectionStrings:PostgreSQL" "your_dev_postgresql_string"
```

## Usage Examples

### Basic Usage
```bash
# Uses appsettings.json
dotnet run -- -s -d -p=SYSMMROLE
```

### Development Environment
```bash
# Uses appsettings.Development.json
DOTNET_ENVIRONMENT=Development dotnet run -- -s -d -p=SYSMMROLE
```

### Production Deployment
```bash
# Uses appsettings.Production.json
DOTNET_ENVIRONMENT=Production dotnet run -- -s -d -p=SYSMMROLE
```

## Troubleshooting

### Configuration Not Found
If you see "Error: Oracle/PostgreSQL connection string not found in appsettings.json":
1. Ensure `appsettings.json` exists in the output directory
2. Verify the `ConnectionStrings` section exists
3. Check that `Oracle` and `PostgreSQL` keys are present

### File Not Copied
If configuration file is not found during runtime:
1. Ensure `appsettings.json` is set to "Copy to Output Directory" in the project file
2. Rebuild the project: `dotnet build`

## Migration Notes

### Before (Hardcoded)
```csharp
var oracleConnString = "Data Source=...";
var postgresConnString = "Host=...";
```

### After (Configuration-Based)
```csharp
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

var oracleConnString = configuration.GetConnectionString("Oracle");
var postgresConnString = configuration.GetConnectionString("PostgreSQL");
```

## Benefits
- ✅ **Security**: No hardcoded credentials in source code
- ✅ **Flexibility**: Easy environment-specific configurations
- ✅ **Maintainability**: Change connections without recompiling
- ✅ **Best Practices**: Follows .NET configuration standards
