# OracleToPostgres Converter - Quick Reference

## Command Options

| Option | Long Form | Description |
|--------|-----------|-------------|
| `-s` | `--schema` | Transfer schema (tables, indexes, etc.) |
| `-d` | `--data` | Transfer data from Oracle to PostgreSQL |
| `-p=PREFIX` | `--prefix=PREFIX` | Specify table prefix(es) to include |
| `-e=TABLE1,TABLE2` | `--exclude=TABLE1,TABLE2` | Exclude tables |
| `-i` | `--ignore-matching` | Force transfer of tables with matching row counts |

## Common Commands

1. **Transfer schema only**:
   ```
   dotnet run -- -s -p=PREFIX
   ```

2. **Transfer schema and data**:
   ```
   dotnet run -- -s -d -p=PREFIX
   ```

3. **Transfer all tables except specific ones**:
   ```
   dotnet run -- -s -d -p= -e=TABLE1,TABLE2
   ```

4. **Transfer multiple prefixes**:
   ```
   dotnet run -- -s -d -p=PREFIX1 -p=PREFIX2
   ```

## Checking Results

Check the transfer log in PostgreSQL:
```sql
SELECT * FROM transfer_log;
```
