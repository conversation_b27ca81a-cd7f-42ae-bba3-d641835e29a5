# OracleToPostgres Converter - Examples

This document provides practical examples of using the OracleToPostgres converter for various scenarios.

## Basic Examples

### 1. Transfer Schema Only

To transfer only the database schema (tables, primary keys, etc.) without data:

```bash
dotnet run -- -s -p=SYSMMROLE
```

This will create all tables that start with "SY<PERSON><PERSON><PERSON><PERSON>" in PostgreSQL with appropriate column types and constraints.

### 2. Transfer Schema and Data

To transfer both schema and data for tables:

```bash
dotnet run -- -s -d -p=SYSMMROLE
```

This will create the table structure and transfer all data from Oracle to PostgreSQL for tables starting with "SYSMMROLE".

### 3. Transfer Multiple Table Prefixes

To transfer schema and data for tables starting with different prefixes:

```bash
dotnet run -- -s -d -p=SYSMMROLE -p=ANOTHER_PREFIX
```

### 4. Transfer All Tables Except Specific Ones

To transfer all tables except specific excluded tables:

```bash
dotnet run -- -s -d -p= -e=TABLE1,TABLE2
```

The empty prefix (`-p=`) means "include all tables", while `-e=TABLE1,TABLE2` excludes those specific tables.

## Advanced Examples

### 5. Force Transfer of Tables with Matching Row Counts

By default, tables with matching row counts in Oracle and PostgreSQL are skipped. To force transfer:

```bash
dotnet run -- -s -d -p=SYSMMROLE -i
```

The `-i` flag (or `--ignore-matching`) tells the converter to process tables even if row counts match.

### 6. Using Long-Form Arguments

All examples can use long-form arguments for better readability:

```bash
dotnet run -- --schema --data --prefix=SYSMMROLE --exclude=TABLE1,TABLE2 --ignore-matching
```

## Log Inspection

After running the converter, you can check the transfer log table in PostgreSQL:

```sql
SELECT * FROM transfer_log;
```

This will show:
- Which tables were processed
- When they were last updated in Oracle
- When they were transferred
- Row counts in both databases
- Processing time

## Handling Large Tables

For very large tables, the converter will automatically use chunking to process the data. No special arguments are needed, but you may want to monitor the process:

```bash
dotnet run -- -s -d -p=LARGE_TABLE_PREFIX
```

The console output will show progress information for large tables as they're processed in chunks.

## Resuming a Failed Transfer

If a transfer fails midway, you can simply run the same command again:

```bash
dotnet run -- -s -d -p=SYSMMROLE
```

The converter will check the transfer_log table and only process tables that:
1. Haven't been transferred yet
2. Have been updated in Oracle since the last transfer
3. Have different row counts between Oracle and PostgreSQL (unless `-i` is specified)
