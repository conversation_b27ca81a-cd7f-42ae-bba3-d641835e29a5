# OracleToPostgres Converter - Operator Documentation

## Overview

OracleToPostgres is a .NET-based tool designed to transfer database schemas and data from Oracle databases to PostgreSQL databases. It handles schema conversion, data migration, and provides logging features to track the migration process.

## Documentation Sections

This documentation is organized into the following sections:

- [Usage Guide](./usage/README.md) - How to use the converter tool
- [Configuration Guide](./configuration/README.md) - How to configure connections and settings
- [Examples](./examples/README.md) - Example usage scenarios and commands
- [Git Workflow Guide](./git-workflow.md) - Git operations, troubleshooting, and best practices
- [Maintenance Guide](./maintenance.md) - Package updates and build cleanup procedures
- [Performance Guide](./performance.md) - Performance monitoring and optimization
- [Troubleshooting Guide](./troubleshooting.md) - Common issues and solutions
- [Quick Reference](./quickref.md) - Quick command reference

## Quick Start

To run the converter, you can use either Make commands (recommended) or dotnet directly:

**Using Make (recommended):**
```bash
make run ARGS="-s -d -p=TABLE_PREFIX"
```

**Using dotnet directly:**
```bash
dotnet run -- -s -d -p=TABLE_PREFIX
```

Where:
- `-s` enables schema transfer
- `-d` enables data transfer
- `-p=` specifies table prefix(es) to include

For more usage examples and Make command features, see the [Usage Guide](./usage/README.md).

## Requirements

- .NET 9.0 or higher
- Access to source Oracle database
- Access to target PostgreSQL database
- Required NuGet packages (automatically installed via project references):
  - Devart.Data (v6.0.235)
  - Devart.Data.Oracle (v10.4.235)
  - Devart.Data.PostgreSQL (v8.4.235)
  - Dapper (v2.1.66)

## Support

For issues or questions, please refer to the project maintainers.
