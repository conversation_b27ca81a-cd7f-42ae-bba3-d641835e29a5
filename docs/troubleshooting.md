# OracleToPostgres Converter - Troubleshooting Guide

This guide provides solutions for common issues you might encounter when using the OracleToPostgres converter.

## Connection Issues

### Oracle Connection Failures

If you receive errors connecting to Oracle:

1. **Check network connectivity** to the Oracle server
   ```bash
   ping [oracle-host]
   ```

2. **Verify Oracle service** is running and accessible
   ```bash
   tnsping [service-name]
   ```

3. **Check credentials** are correct in the connection string

4. **Verify firewall settings** allow connections to Oracle's port (typically 1521)

### PostgreSQL Connection Failures

If you receive errors connecting to PostgreSQL:

1. **Check network connectivity** to the PostgreSQL server
   ```bash
   ping [postgres-host]
   ```

2. **Verify PostgreSQL service** is running
   ```bash
   pg_isready -h [postgres-host] -p 5432
   ```

3. **Check credentials** are correct in the connection string

4. **Verify firewall settings** allow connections to PostgreSQL's port (typically 5432)

## Data Transfer Issues

### Missing Tables

If expected tables are not being processed:

1. **Check table prefixes** specified with `-p=` arguments
2. **Verify table existence** in Oracle schema
3. **Check for case sensitivity** - table names may be case-sensitive

### Data Type Conversion Errors

If you encounter data type conversion errors:

1. **Check for unsupported data types** in Oracle tables
2. **Look for special characters or binary data** that may not convert properly
3. **Review conversion logic** in the `ConvertOracleTypeToPostgres` method

### Transfer Log Errors

If the transfer log table shows errors:

1. **Check the status column** for specific error messages
2. **Review console output** for detailed error information
3. **Check row counts** between Oracle and PostgreSQL to identify discrepancies

## Performance Issues

### Slow Transfer Speed

If data transfer is slower than expected:

1. **Check network bandwidth** between Oracle and PostgreSQL servers
2. **Review table sizes** - very large tables will take longer
3. **Consider using bulk transfer mode** for better performance
4. **Check for blocking queries** on either database

### Memory Usage

If the converter uses excessive memory:

1. **Reduce batch sizes** for large tables
2. **Process fewer tables** at once
3. **Monitor memory usage** with appropriate tools

## Common Error Codes

### Error: ORA-12154

"TNS:could not resolve the connect identifier specified"

**Solution**: Check your Oracle connection string, specifically the service name and host.

### Error: ORA-01017

"Invalid username/password; logon denied"

**Solution**: Verify Oracle credentials in the connection string.

### Error: 28P01

PostgreSQL authentication error.

**Solution**: Check PostgreSQL username and password.

### Error: 3D000

"Database does not exist" in PostgreSQL.

**Solution**: Verify the database name in the PostgreSQL connection string.

## Getting More Information

For detailed debugging:

1. **Enable verbose logging** (add code to increase logging detail)
2. **Check database server logs** for both Oracle and PostgreSQL
3. **Review network traffic** if appropriate

## Reporting Issues

When reporting issues to the project maintainers, please include:

1. **Command-line arguments** used
2. **Error messages** received
3. **Table information** (number of rows, column types)
4. **Database versions** for both Oracle and PostgreSQL
