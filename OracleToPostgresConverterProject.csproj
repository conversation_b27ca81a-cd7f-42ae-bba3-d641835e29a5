<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Devart.Data" Version="6.0.235" />
    <PackageReference Include="Devart.Data.Oracle" Version="10.4.235" />
    <PackageReference Include="Devart.Data.PostgreSQL" Version="8.4.235" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- MSBuild target to remove unwanted runtime folders after build -->
  <Target Name="RemoveOldRuntimeFolders" AfterTargets="Build">
    <ItemGroup>
      <OldRuntimeFolders Include="$(OutputPath)runtimes/win/lib/netcoreapp2.0/**" />
      <OldRuntimeFolders Include="$(OutputPath)runtimes/win/lib/netstandard2.0/**" />
    </ItemGroup>
    <Delete Files="@(OldRuntimeFolders)" />
    <RemoveDir Directories="$(OutputPath)runtimes/win/lib/netcoreapp2.0;$(OutputPath)runtimes/win/lib/netstandard2.0" Condition="Exists('$(OutputPath)runtimes/win/lib/netcoreapp2.0') Or Exists('$(OutputPath)runtimes/win/lib/netstandard2.0')" />
  </Target>

</Project>