# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a .NET 9.0 command-line application that converts Oracle database schemas and transfers data to PostgreSQL databases. The tool uses Devart data providers for Oracle and PostgreSQL connectivity, with <PERSON><PERSON> for data access.

## Architecture

### Core Components

- **OracleToPostgresConverter.cs**: Single-file application containing all conversion logic
- **LogEntry class**: Data model for transfer tracking
- **OracleToPostgresConverter class**: Main converter with connection management and transfer methods
- **Program class**: Entry point with command-line argument parsing

### Key Features

- Schema conversion from Oracle to PostgreSQL
- Data transfer with row count comparison
- Transfer logging via `transfer_log` table in PostgreSQL
- Bulk transfer and row-by-row processing options
- Large table handling with chunked processing
- Automatic cleanup of legacy runtime directories

## Build and Development Commands

### Basic Commands
```bash
# Build project
dotnet build
make build

# Run application
dotnet run -- [options]
make run

# Clean build artifacts
dotnet clean
make clean

# Restore NuGet packages
dotnet restore
make restore

# Publish application
dotnet publish --configuration Release
make publish
```

### Development Commands
```bash
# Debug build
make debug

# Run with debug configuration
make run-debug

# Run with custom arguments
make run-with-args ARGS="-s -d -p=TABLE_PREFIX"

# Check for outdated packages
dotnet list package --outdated
```

## Application Usage

### Command Line Options
- `-s, --schema`: Transfer schema (tables, indexes, etc.)
- `-d, --data`: Transfer data from Oracle to PostgreSQL
- `-p=PREFIX, --prefix=PREFIX`: Table prefix(es) to include
- `-e=TABLE1,TABLE2, --exclude=TABLE1,TABLE2`: Exclude specific tables
- `-i, --ignore-matching`: Process tables even if row counts match

### Common Usage Examples
```bash
# Schema and data transfer for specific prefix
dotnet run -- -s -d -p=SYSMMROLE

# All tables schema and data transfer
dotnet run -- -s -d -p=

# Multiple prefixes with exclusions
dotnet run -- -s -d -p=PREFIX1 -p=PREFIX2 -e=TEMP_TABLE,LOG_TABLE

# Force transfer ignoring row count matches
dotnet run -- -s -d -p=PREFIX -i
```

## Database Configuration

### Connection Strings
Connection strings are currently hardcoded in OracleToPostgresConverter.cs:1725-1726. For production:
- Move to appsettings.json or environment variables
- Use secure secret management for sensitive credentials
- Never commit actual connection strings to version control

### Transfer Methods
The converter automatically selects transfer methods based on table characteristics:
- **TransferBulk**: Fast bulk transfers for large tables
- **TransferRowByRow**: Reliable row-by-row for complex data
- **ProcessDataDirectly**: Custom processing for specific data types
- **TransferLargeTable**: Chunked processing for very large tables

## Package Management

### Current Dependencies
- Devart.Data (v6.0.235)
- Devart.Data.Oracle (v10.4.235)
- Devart.Data.PostgreSQL (v8.4.235)
- Dapper (v2.1.66)

### Update Process
1. Check for updates: `dotnet list package --outdated`
2. Update individually: `dotnet add package [PackageName] --version [Version]`
3. Test build: `dotnet build`
4. Document changes in docs/maintenance.md

## Build Configuration

### MSBuild Targets
The project includes automatic cleanup of legacy runtime directories:
- Removes `runtimes/win/lib/netcoreapp2.0/` and `runtimes/win/lib/netstandard2.0/`
- Runs after every build (Debug and Release)
- Prevents accumulation of unnecessary framework libraries

### Framework Configuration
- Target: .NET 9.0
- Nullable reference types enabled
- Implicit usings enabled

## Code Structure

### Main Entry Point
- **Program.Main()**: Command-line parsing and application orchestration (line 1685)
- Validates arguments and displays usage information
- Initializes converter with connection strings and options

### Converter Architecture
- **Constructor**: Establishes database connections and creates log table
- **GetTableList()**: Filters tables based on prefixes and exclusions
- **Transfer methods**: Various strategies for different data scenarios
- **Logging**: Comprehensive transfer tracking in PostgreSQL

## Testing

The project currently has no automated tests. To verify functionality:
1. Test connection to both Oracle and PostgreSQL databases
2. Verify schema conversion with sample tables
3. Validate data transfer accuracy with row count comparisons
4. Check transfer log entries for completeness

## Documentation

Comprehensive documentation is available in the `docs/` directory:
- **Usage Guide**: Command-line options and examples
- **Configuration Guide**: Database connection setup
- **Maintenance Guide**: Package updates and cleanup procedures
- **Examples**: Common usage scenarios