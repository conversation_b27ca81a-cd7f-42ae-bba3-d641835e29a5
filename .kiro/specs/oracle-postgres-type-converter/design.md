# Design Document

## Overview

The Oracle to PostgreSQL Type Converter is implemented as a single method that provides deterministic mapping between Oracle database types and PostgreSQL equivalents. The design prioritizes column naming conventions over Oracle type-based mapping to allow for domain-specific overrides.

## Architecture

The converter follows a simple decision tree architecture:

1. **Priority 1**: Column naming convention analysis
2. **Priority 2**: Oracle type-based mapping
3. **Priority 3**: Default fallback to TEXT

This hierarchical approach ensures that business logic encoded in column names takes precedence over generic type mapping.

## Components and Interfaces

### ConvertOracleTypeToPostgres Method

**Signature:**
```csharp
private string ConvertOracleTypeToPostgres(string oracleType, string columnName, decimal? length)
```

**Input Parameters:**
- `oracleType`: The Oracle data type as a string (case-insensitive)
- `columnName`: The column name used for naming convention analysis
- `length`: Optional decimal value for column length (reserved for future use)

**Output:**
- Returns a PostgreSQL type name as a string

### Decision Logic Flow

```
Input: oracleType, columnName, length
    ↓
Check columnName suffix patterns
    ↓
If "_dt" suffix → Return "TIMESTAMP"
    ↓
If "_nm" suffix → Return "DECIMAL"
    ↓
Switch on oracleType.ToUpper()
    ↓
Match Oracle type → Return mapped PostgreSQL type
    ↓
Default case → Return "TEXT"
```

## Data Models

### Type Mapping Table

| Oracle Type | PostgreSQL Type | Notes |
|-------------|-----------------|-------|
| VARCHAR2    | TEXT           | Variable-length character data |
| NVARCHAR2   | TEXT           | Unicode variable-length character data |
| RAW         | TEXT           | Binary data treated as text |
| NUMBER      | NUMERIC        | Numeric data with precision |
| DATE        | TIMESTAMP      | Date and time data |
| CLOB        | TEXT           | Character large object |
| BLOB        | BYTEA          | Binary large object |
| LONG RAW    | BYTEA          | Long binary data |

### Naming Convention Overrides

| Column Suffix | PostgreSQL Type | Use Case |
|---------------|-----------------|----------|
| _dt           | TIMESTAMP      | Date/time columns |
| _nm           | DECIMAL        | Numeric/amount columns |

## Error Handling

### Unsupported Types
- **Strategy**: Graceful degradation
- **Implementation**: Default to TEXT type for any unrecognized Oracle type
- **Rationale**: Ensures migration continues even with unknown types

### Null/Empty Inputs
- **Current State**: No explicit null checking implemented
- **Behavior**: Relies on .NET string method behavior for null inputs
- **Risk**: Potential NullReferenceException if inputs are null

### Case Sensitivity
- **Oracle Types**: Converted to uppercase using `ToUpper()` for consistent matching
- **Column Names**: Case-insensitive suffix matching using `StringComparison.OrdinalIgnoreCase`

## Testing Strategy

### Unit Test Categories

1. **Oracle Type Mapping Tests**
   - Test each supported Oracle type maps to correct PostgreSQL type
   - Test case-insensitive Oracle type matching
   - Test unsupported type defaults to TEXT

2. **Naming Convention Tests**
   - Test _dt suffix override behavior
   - Test _nm suffix override behavior
   - Test case-insensitive suffix matching
   - Test naming convention precedence over type mapping

3. **Edge Case Tests**
   - Test with null/empty inputs
   - Test with mixed case inputs
   - Test with special characters in column names
   - Test length parameter handling (currently unused)

4. **Integration Tests**
   - Test with real Oracle schema metadata
   - Test complete conversion workflow

### Test Data Examples

```csharp
// Type mapping tests
("VARCHAR2", "any_column", null) → "TEXT"
("varchar2", "any_column", null) → "TEXT"
("NUMBER", "any_column", null) → "NUMERIC"
("UNSUPPORTED_TYPE", "any_column", null) → "TEXT"

// Naming convention tests
("VARCHAR2", "created_dt", null) → "TIMESTAMP"
("NUMBER", "amount_nm", null) → "DECIMAL"
("VARCHAR2", "UPDATED_DT", null) → "TIMESTAMP"
("CLOB", "price_NM", null) → "DECIMAL"
```

## Performance Considerations

### Time Complexity
- **Best Case**: O(1) - Direct naming convention match
- **Average Case**: O(1) - Switch statement lookup
- **Worst Case**: O(1) - All operations are constant time

### Memory Usage
- **Minimal**: Only string operations, no object allocation
- **String Operations**: Uses built-in .NET string methods for efficiency

### Scalability
- **Stateless**: Method has no side effects or state dependencies
- **Thread-Safe**: Pure function suitable for concurrent execution
- **Cacheable**: Results are deterministic and could be cached if needed