# Requirements Document

## Introduction

The Oracle to PostgreSQL Type Converter is a feature that automatically maps Oracle database column types to their equivalent PostgreSQL types during database migration. The converter uses both the Oracle data type and column naming conventions to determine the most appropriate PostgreSQL type mapping.

## Requirements

### Requirement 1

**User Story:** As a database administrator, I want the system to automatically convert Oracle data types to PostgreSQL equivalents, so that I can migrate databases without manually mapping each column type.

#### Acceptance Criteria

1. WHEN the system encounters a VARCHAR2 Oracle type THEN the system SHALL convert it to TEXT PostgreSQL type
2. WHEN the system encounters a NVARCHAR2 Oracle type THEN the system SHALL convert it to TEXT PostgreSQL type
3. WHEN the system encounters a RAW Oracle type THEN the system SHALL convert it to TEXT PostgreSQL type
4. WHEN the system encounters a NUMBER Oracle type THEN the system SHALL convert it to NUMERIC PostgreSQL type
5. WHEN the system encounters a DATE Oracle type THEN the system SHALL convert it to TIMESTAMP PostgreSQL type
6. WHEN the system encounters a CLOB Oracle type THEN the system SHALL convert it to TEXT PostgreSQL type
7. WHEN the system encounters a BLOB Oracle type THEN the system SHALL convert it to BYTEA PostgreSQL type
8. WHEN the system encounters a LONG RAW Oracle type THEN the system SHALL convert it to BYTEA PostgreSQL type
9. WHEN the system encounters an unsupported Oracle type THEN the system SHALL default to TEXT PostgreSQL type

### Requirement 2

**User Story:** As a database administrator, I want the system to use column naming conventions to override default type mappings, so that columns with specific naming patterns get more appropriate data types.

#### Acceptance Criteria

1. WHEN a column name ends with "_dt" (case insensitive) THEN the system SHALL convert it to TIMESTAMP PostgreSQL type regardless of the Oracle type
2. WHEN a column name ends with "_nm" (case insensitive) THEN the system SHALL convert it to DECIMAL PostgreSQL type regardless of the Oracle type
3. WHEN a column has naming convention suffixes THEN the naming convention SHALL take precedence over Oracle type-based mapping

### Requirement 3

**User Story:** As a database administrator, I want the type conversion to be case-insensitive for Oracle types, so that the system works regardless of how the Oracle schema defines type names.

#### Acceptance Criteria

1. WHEN Oracle types are provided in any case combination THEN the system SHALL perform case-insensitive matching
2. WHEN comparing column naming conventions THEN the system SHALL perform case-insensitive suffix matching

### Requirement 4

**User Story:** As a database administrator, I want the system to accept column length information as input, so that future enhancements can utilize this data for more precise type mapping.

#### Acceptance Criteria

1. WHEN the conversion method is called THEN the system SHALL accept an optional length parameter
2. WHEN length information is provided THEN the system SHALL store it for potential future use