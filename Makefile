# Makefile for OracleToPostgresConverter .NET project

# Project name
PROJECT_NAME := OracleToPostgresConverterProject

# Configuration options
CONFIG ?= Release
FRAMEWORK := net9.0

# Directories
SRC_DIR := .
BIN_DIR := $(SRC_DIR)/bin/$(CONFIG)/$(FRAMEWORK)
OBJ_DIR := $(SRC_DIR)/obj

# Commands
DOTNET := dotnet

.PHONY: all build clean run test restore publish check-deps upgrade-deps

# Default target
all: build

# Build the project
build:
	@echo "Building $(PROJECT_NAME)..."
	@$(DOTNET) build --configuration $(CONFIG)

# Clean build output
clean:
	@echo "Cleaning build output..."
	@$(DOTNET) clean
	@echo "Removing bin and obj directories..."
	@rm -rf $(BIN_DIR) $(OBJ_DIR)

# Run the application
run: build
	@echo "Running application..."
ifdef ARGS
	@echo "Arguments: $(ARGS)"
	@$(DOTNET) run --configuration $(CONFIG) -- $(ARGS)
else
	@$(DOTNET) run --configuration $(CONFIG)
endif

# Run with specific arguments (legacy target - use 'run ARGS="..."' instead)
run-with-args: build
	@echo "Running application with arguments..."
	@$(DOTNET) run --configuration $(CONFIG) -- $(ARGS)

# Run tests if they exist
test:
	@echo "Running tests..."
	@$(DOTNET) test

# Restore packages
restore:
	@echo "Restoring packages..."
	@$(DOTNET) restore

# Publish the application
publish:
	@echo "Publishing application..."
	@$(DOTNET) publish --configuration $(CONFIG) --self-contained false

# Build with debug configuration
debug:
	@$(MAKE) build CONFIG=Debug

# Run with debug configuration
run-debug:
	@$(MAKE) run CONFIG=Debug

# Check for outdated packages
check-deps:
	@echo "Checking for outdated packages..."
	@$(DOTNET) list package --outdated

# Upgrade packages to latest versions
upgrade-deps:
	@echo "Upgrading packages to latest versions..."
	@echo "Note: This will update packages to their latest compatible versions"
	@read -p "Continue? [y/N]: " confirm && [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ] || exit 1
	@$(DOTNET) list package --outdated --format json | \
		jq -r '.projects[].frameworks[].topLevelPackages[]? | select(.resolvedVersion != .latestVersion) | .id' | \
		while read package; do \
			echo "Upgrading $$package..."; \
			$(DOTNET) add package "$$package"; \
		done || echo "jq not found. Using alternative method..."; \
		$(DOTNET) list package --outdated | \
		grep -E "^   >" | \
		awk '{print $$2}' | \
		while read package; do \
			echo "Upgrading $$package..."; \
			$(DOTNET) add package "$$package"; \
		done
	@echo "Package upgrade complete. Running restore..."
	@$(MAKE) restore

# Help information
help:
	@echo "Makefile commands:"
	@echo "  make          - Build the project (default)"
	@echo "  make build    - Build the project"
	@echo "  make clean    - Clean build outputs"
	@echo "  make run      - Run the application"
	@echo "  make run ARGS='arg1 arg2...' - Run with arguments"
	@echo "  make run-with-args ARGS='arg1 arg2...' - Run with arguments (legacy)"
	@echo "  make test     - Run tests if they exist"
	@echo "  make restore  - Restore NuGet packages"
	@echo "  make publish  - Publish the application"
	@echo "  make debug    - Build with Debug configuration"
	@echo "  make run-debug - Run with Debug configuration"
	@echo "  make check-deps - Check for outdated packages"
	@echo "  make upgrade-deps - Upgrade packages to latest versions"
	@echo ""
	@echo "Example usage with arguments:"
	@echo "  make run ARGS='-s -d -p=CDISC'"
	@echo "  make run ARGS='-s -p=USER_'"
	@echo "  make run ARGS='-d -p=EMPLOYEE -p=DEPARTMENT'"
