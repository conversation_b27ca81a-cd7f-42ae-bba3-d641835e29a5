﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 25.0.1706.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleToPostgresConverterProject", "OracleToPostgresConverterProject.csproj", "{00CB9CB9-C60A-4CBA-A9A7-C9E25CA06CD1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{00CB9CB9-C60A-4CBA-A9A7-C9E25CA06CD1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00CB9CB9-C60A-4CBA-A9A7-C9E25CA06CD1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00CB9CB9-C60A-4CBA-A9A7-C9E25CA06CD1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00CB9CB9-C60A-4CBA-A9A7-C9E25CA06CD1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1F8E46CC-C35C-4500-85ED-9344317B6146}
	EndGlobalSection
EndGlobal
