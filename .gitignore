# .NET 9 specific
*.suo
*.user
*.userosscache
*.sln.docstates
*.dll.config
*.runtimeconfig.json
*.runtimeconfig.dev.json
*.deps.json

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/
.vs/

# .NET 9 publish output
publish/
PublishProfiles/
*.pubxml
*.pubxml.user
*.publishproj

# .NET 9 runtime directories
runtimes/
runtime/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUNIT
*.VisualState.xml
TestResult.xml
nunit-*.xml

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity
_TeamCity*

# DotCover
*.dotCover

# AxoCover
.axoCover/*
!.axoCover/settings.json

# Visual Studio code coverage results
*.coverage
*.coveragexml

# Visual Studio cache files
*.[Cc]ache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs
*.userprefs

# Visual Studio temp directory
.vs/

# Visual Studio for Mac
.vscode/
.idea/
.DS_Store

# .NET Core/.NET 9
project.lock.json
project.fragment.lock.json
artifacts/
*.globalconfig

# .NET 9 Hot Reload
*.hot_reload_cache
.dotnet/
global.json.bak

# AOT compilation
*.aot_analysis_warnings
native/

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# NuGet cache files
project.nuget.cache
*.nuget.cache
.nuget/
packages/
.packages/

# .NET 9 NuGet specific
*.nuget.dgspec.json
project.assets.json
*.nuget.g.props
*.nuget.g.targets

# Local History for Visual Studio
.localhistory/

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
FakesAssemblies/

# Cache directories and files
**/cache/
**/Cache/
*.cache
.cache/
cache.txt
cache.json
cache.xml

# Temporary cache files
*.tmp.cache
*.temp.cache

# Application-specific cache
app.cache
application.cache
data.cache
query.cache
result.cache

# Database cache
db.cache
database.cache
*.db.cache

# Oracle-specific cache
oracle.cache
*.ora.cache
tnsnames.cache

# PostgreSQL-specific cache
postgres.cache
*.pg.cache
pg_stat_tmp/

# Migration cache
migration.cache
*.migration.cache
schema.cache

# Build and compilation cache
build.cache
compile.cache
*.build.cache

# IDE and editor cache
.vscode/settings.json.cache
.idea/workspace.xml.cache

# Configuration files with sensitive data
# Uncomment these lines to protect production configurations:
# appsettings.Production.json
# appsettings.*.json
# !appsettings.json
# !appsettings.Development.json

# User secrets and environment files
.env
.env.local
.env.production
*.env
secrets.json
user-secrets/

