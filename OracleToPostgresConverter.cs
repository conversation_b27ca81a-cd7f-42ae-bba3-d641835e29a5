using System;
using System.Data;
using Devart.Data.Oracle;
using Devart.Data.PostgreSql;
using Dapper;
using Microsoft.Extensions.Configuration;

public class LogEntry
{
    public string? table_name { get; set; }
    public DateTime last_oracle_update { get; set; }
    public DateTime last_transfer_time { get; set; }
    public long oracle_row_count { get; set; }
    public long postgres_row_count { get; set; }
    public TimeSpan process_time { get; set; }
    public string? process_time_string { get; set; }
    public string? status { get; set; }
    public int statuscd { get; set; }
}

public class OracleToPostgresConverter
{
    private bool disposed = false;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                oracleConnection?.Dispose();
                pgConnection?.Dispose();
                CloseAllWriters();
            }

            disposed = true;
        }
    }

    ~OracleToPostgresConverter()
    {
        Dispose(false);
    }

    private OracleConnection oracleConnection;
    private PgSqlConnection pgConnection;

    private bool ignoreMatchingRowCounts;
    private List<string> excludedTables;

    public OracleToPostgresConverter(string oracleConnString, string postgresConnString, bool ignoreMatchingRowCounts, List<string> excludedTables)
    {
        this.ignoreMatchingRowCounts = ignoreMatchingRowCounts;
        this.columnOrdinals = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
        oracleConnection = new OracleConnection(oracleConnString);
        pgConnection = new PgSqlConnection(postgresConnString);
        try
        {
            oracleConnection.Open();
            pgConnection.Open();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error opening connections: {ex.Message}");
            throw;
        }

        CreateLogTable();
        this.excludedTables = excludedTables.Select(t => t.ToUpper()).ToList();
    }

    private void CreateLogTable()
    {
        var createLogTableSql = @"
            CREATE TABLE IF NOT EXISTS transfer_log (
                table_name TEXT PRIMARY KEY,
                last_oracle_update TIMESTAMP,
                last_transfer_time TIMESTAMP,
                oracle_row_count BIGINT,
                postgres_row_count BIGINT,
                process_time INTERVAL,
                process_time_string TEXT,
                status TEXT,
                statuscd INTEGER
            );";
        pgConnection.Execute(createLogTableSql);
    }

    public List<string> GetTableList(List<string> tablePrefixes, bool transferSchema, bool transferData)
    {
        var tables = GetOracleTables(oracleConnection);
        var filteredTables = tablePrefixes.Count == 0 || tablePrefixes.Contains("")
            ? tables.ToList()
            : tables.Where(t => tablePrefixes.Any(prefix => t.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))).ToList();

        if (excludedTables.Any())
        {
            filteredTables = filteredTables.Where(t => !excludedTables.Contains(t, StringComparer.OrdinalIgnoreCase)).ToList();
        }

        return filteredTables.Where(table =>
        {
            if (transferSchema)
            {
                // Always include table if schema transfer is requested
                return true;
            }
            else if (transferData)
            {
                // Check if data transfer is needed
                return ShouldTransferTable(table);
            }
            return false;
        }).ToList();
    }

    private bool ShouldTransferTable(string tableName)
    {
        var oracleUpdateTime = GetOracleTableUpdateTime(tableName);
        var oracleRowCount = GetTableRowCount(oracleConnection, tableName);
        var logEntry = GetLogEntry(tableName);

        if (logEntry == null)
        {
            return true; // Table hasn't been transferred before
        }

        long postgresRowCount = logEntry.postgres_row_count; // GetTableRowCount(pgConnection, tableName);

        if (oracleUpdateTime > logEntry.last_oracle_update || oracleRowCount != postgresRowCount)
        {
            return true; // Oracle table has been updated or row count has changed
        }

        if (ignoreMatchingRowCounts)
        {
            if (oracleRowCount != postgresRowCount)
            {
                return true; // Row counts don't match
            }
        }

        return false; // No changes, skip transfer
    }


    private void UpdateLogEntryRowCount(string tableName, long postgresRowCount)
    {
        var updateSql = @"
            UPDATE transfer_log 
            SET postgres_row_count = :postgresRowCount                
            WHERE table_name = :tableName";

        using (var command = new PgSqlCommand(updateSql, pgConnection))
        {
            command.Parameters.AddWithValue("postgresRowCount", postgresRowCount);
            command.Parameters.AddWithValue("tableName", tableName);
            command.ExecuteNonQuery();
        }
    }

    private DateTime GetOracleTableUpdateTime(string tableName)
    {
        var sql = $"SELECT MAX(LAST_DDL_TIME) FROM ALL_OBJECTS WHERE OBJECT_NAME = :tableName";
        return oracleConnection.QuerySingle<DateTime>(sql, new { tableName });
    }

    private LogEntry GetLogEntry(string tableName)
    {
        var sql = "SELECT * FROM transfer_log WHERE table_name = :tableName";
        return pgConnection.QueryFirstOrDefault<LogEntry>(sql, new { tableName })!;
    }

    private void UpdateLogEntry(string tableName, DateTime oracleUpdateTime, long oracleRowCount, long postgresRowCount, TimeSpan processTime, string status, bool isSchemaOnly = false)
    {
        // Determine statuscd based on the status
        int statuscd = -1;
        if (!status.StartsWith("Error", StringComparison.OrdinalIgnoreCase))
        {
            // Compare row counts and update status
            if (oracleRowCount == postgresRowCount)
            {
                statuscd = 1;
                status = "OK - Counts Match";
            }
            else
            {
                statuscd = 0;
                status = $"Warning - Count Mismatch (Oracle: {oracleRowCount}, Postgres: {postgresRowCount})";
            }
        }

        string processTimeString = FormatTimeSpan(processTime);

        var existingLog = GetLogEntry(tableName);

        if (existingLog != null)
        {
            // Log entry exists, update it
            var updateSql = @"
                UPDATE transfer_log 
                SET last_oracle_update = :oracleUpdateTime,
                    last_transfer_time = CURRENT_TIMESTAMP,
                    oracle_row_count = :oracleRowCount,
                    postgres_row_count = :postgresRowCount,
                    process_time = :processTime,
                    process_time_string = :processTimeString,
                    status = :status,
                    statuscd = :statuscd
                WHERE table_name = :tableName";

            using (var command = new PgSqlCommand(updateSql, pgConnection))
            {
                command.Parameters.AddWithValue("oracleUpdateTime", oracleUpdateTime);
                command.Parameters.AddWithValue("oracleRowCount", oracleRowCount);
                command.Parameters.AddWithValue("postgresRowCount", postgresRowCount);
                command.Parameters.AddWithValue("processTime", processTime);
                command.Parameters.AddWithValue("processTimeString", processTimeString);
                command.Parameters.AddWithValue("status", status);
                command.Parameters.AddWithValue("statuscd", statuscd);
                command.Parameters.AddWithValue("tableName", tableName);
                command.ExecuteNonQuery();
            }
        }
        else
        {
            // No log entry exists, insert a new one
            var insertSql = @"
                INSERT INTO transfer_log (table_name, last_oracle_update, last_transfer_time, oracle_row_count, postgres_row_count, process_time, process_time_string, status, statuscd)
                VALUES (:tableName, :oracleUpdateTime, CURRENT_TIMESTAMP, :oracleRowCount, :postgresRowCount, :processTime, :processTimeString, :status, :statuscd)";

            using (var command = new PgSqlCommand(insertSql, pgConnection))
            {
                command.Parameters.AddWithValue("tableName", tableName);
                command.Parameters.AddWithValue("oracleUpdateTime", oracleUpdateTime);
                command.Parameters.AddWithValue("oracleRowCount", oracleRowCount);
                command.Parameters.AddWithValue("postgresRowCount", postgresRowCount);
                command.Parameters.AddWithValue("processTime", processTime);
                command.Parameters.AddWithValue("processTimeString", processTimeString);
                command.Parameters.AddWithValue("status", status);
                command.Parameters.AddWithValue("statuscd", statuscd);
                command.ExecuteNonQuery();
            }
        }

        if (isSchemaOnly)
        {
            // If it's a schema-only operation, update the log to indicate data transfer is needed
            var schemaUpdateSql = @"
                UPDATE transfer_log 
                SET last_oracle_update = :minDate,
                    last_transfer_time = :minDate,
                    oracle_row_count = 0,
                    postgres_row_count = 0,
                    status = 'Schema Only - Data Transfer Needed'
                WHERE table_name = :tableName";

            using (var command = new PgSqlCommand(schemaUpdateSql, pgConnection))
            {
                command.Parameters.AddWithValue("minDate", DateTime.MinValue);
                command.Parameters.AddWithValue("tableName", tableName);
                command.ExecuteNonQuery();
            }
        }
    }

    public bool ConvertSchema(List<string> tables)
    {
        try
        {
            foreach (var table in tables)
            {
                DropPostgresTableIfExists(pgConnection, table);
                var columns = GetOracleColumnsWithPrimaryKey(oracleConnection, table);
                CreatePostgresTable(pgConnection, oracleConnection, table, columns);

                // After creating the schema, mark the table for data transfer
                UpdateLogEntry(table, DateTime.MinValue, 0, 0, TimeSpan.Zero, "Schema Only", true);
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error converting schema: {ex.Message}");
            return false;
        }
    }

    private bool ShouldSkipTable(string tableName)
    {
        long oracleRowCount = GetTableRowCount(oracleConnection, tableName);
        long postgresRowCount = GetTableRowCount(pgConnection, tableName);

        if (oracleRowCount == postgresRowCount)
        {
            Console.WriteLine($"Skipping table {tableName} due to matching row counts");
            return true;
        }
        return false;
    }

    private long GetTableRowCount(IDbConnection connection, string tableName, string defaultIfNull = "")
    {
        string sql = $"SELECT COUNT(*) FROM {tableName} {defaultIfNull}";
        try
        {
            return connection.ExecuteScalar<long>(sql.Trim());
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting row count for table {tableName}: {ex.Message}");
            return -1; // Return -1 if there's an error
        }
    }

    protected class ColumnInfo
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public decimal? Length { get; set; }
        public bool IsNullable { get; set; }
        public string DefaultValue { get; set; }
        public bool IsPrimaryKey { get; set; }

        public ColumnInfo(string name, string type, decimal? length, decimal isNullable, string defaultValue, decimal isPrimaryKey)
        {
            Name = name;
            Type = type;
            Length = length;
            IsNullable = isNullable == 1;
            DefaultValue = defaultValue;
            IsPrimaryKey = isPrimaryKey == 1;
        }
    }

    private IEnumerable<ColumnInfo> GetOracleColumnsWithPrimaryKey(OracleConnection connection, string tableName)
    {
        var query = @"
            SELECT 
                c.column_name AS Name, 
                c.data_type AS Type, 
                c.data_length AS Length, 
                CASE WHEN c.nullable = 'Y' THEN 1 ELSE 0 END AS isNullable, 
                c.data_default AS DefaultValue,
                CASE WHEN cons.constraint_type = 'P' THEN 1 ELSE 0 END AS isPrimaryKey
            FROM user_tab_columns c
            LEFT JOIN user_cons_columns cols ON c.table_name = cols.table_name AND c.column_name = cols.column_name
            LEFT JOIN user_constraints cons ON cols.constraint_name = cons.constraint_name AND cons.constraint_type = 'P'
            WHERE c.table_name = :tableName
            ORDER BY c.column_id";

        return connection.Query<ColumnInfo>(query, new { tableName });
    }

    private IEnumerable<ColumnInfo> GetOracleColumns(OracleConnection connection, string tableName)
    {
        var query = @"
                SELECT 
                    c.column_name AS Name, 
                    c.data_type AS Type, 
                    c.data_length AS Length, 
                    CASE WHEN c.nullable = 'Y' THEN 1 ELSE 0 END AS isNullable, 
                    c.data_default AS DefaultValue,
                    0 AS isPrimaryKey
                FROM user_tab_columns c
                WHERE c.table_name = :tableName ";
        return connection.Query<ColumnInfo>(query, new { tableName });
    }

    private void CreatePostgresTable(PgSqlConnection connection, OracleConnection oracleConnection, string tableName, IEnumerable<ColumnInfo> columns)
    {
        var columnDefinitions = columns
            .GroupBy(c => c.Name)
            .SelectMany(g => g.Select((c, i) =>
            {
                var columnName = c.Name;
                var defaultValue = c.DefaultValue?.Trim().ToUpper() == "SYS_GUID()" ? "gen_random_uuid()" : null;
                // Check for duplicate column names
                if (g.Count() > 1 && i > 0)
                    return null; // Skip duplicate columns by returning null
                else
                    return $"{columnName} {ConvertOracleTypeToPostgres(c.Type, c.Name, c.Length)} {(c.IsNullable ? "NULL" : "NOT NULL")} {(defaultValue != null ? $"DEFAULT {defaultValue}" : "")}";
            }));

        var primaryKeyColumns = columns.Where(c => c.IsPrimaryKey).Select(c => c.Name);
        string? pkDefinition;

        if (primaryKeyColumns.Any())
        {
            pkDefinition = $"PRIMARY KEY ({string.Join(", ", primaryKeyColumns)})";
        }
        else if (columns.Any(c => c.Name.Equals("IGUID32_FT", StringComparison.OrdinalIgnoreCase)))
        {
            pkDefinition = "PRIMARY KEY (IGUID32_FT)";
        }
        else
        {
            pkDefinition = null;
        }

        if (pkDefinition != null)
        {
            columnDefinitions = columnDefinitions.Concat(new[] { pkDefinition });
        }

        var createTableSql = $"CREATE TABLE {tableName} ({string.Join(", ", columnDefinitions.Where(c => c != null))});";

        connection.Execute(createTableSql);

        // If IGUID32_FT was set as primary key, log this information
        if (pkDefinition != null && pkDefinition.Contains("IGUID32_FT"))
        {
            Console.WriteLine($"Table {tableName}: No primary key found, IGUID32_FT set as primary key.");
        }
    }

    private IEnumerable<string> GetOracleTables(OracleConnection connection)
    {
        return connection.Query<string>("SELECT table_name FROM user_tables");
    }


    private void DropPostgresTableIfExists(PgSqlConnection connection, string tableName)
    {
        var dropTableSql = $"DROP TABLE IF EXISTS {tableName};";
        connection.Execute(dropTableSql);
    }

    private string ConvertOracleTypeToPostgres(string oracleType, string columnName, decimal? length)
    {
        if (columnName.EndsWith("_dt", StringComparison.OrdinalIgnoreCase))
            return "TIMESTAMP";
        else if (columnName.EndsWith("_nm", StringComparison.OrdinalIgnoreCase))
            return "DECIMAL";

        switch (oracleType.ToUpper())
        {
            case "VARCHAR2":
            case "NVARCHAR2":
            case "RAW":
                return "TEXT";
            case "NUMBER":
                return "NUMERIC";
            case "DATE":
                return "TIMESTAMP";
            case "CLOB":
                return "TEXT";
            case "BLOB":
            case "LONG RAW":
                return "BYTEA";
            default:
                return "TEXT"; // Default to TEXT for unsupported types
        }
    }

    private int TransferRDCMMIMAGWithoutBlob(string tableName)
    {
        var columns = GetOracleColumns(oracleConnection, tableName)
            .Where(c => c.Name.ToUpper() != "CRFIMAGE_BL")
            .ToList();
        /*
        var columns = GetOracleColumns(oracleConnection, tableName)
            .ToList();
        */
        var selectFields = string.Join(", ", columns.Select(c => c.Name));
        var selectDataSql = $"SELECT {selectFields} FROM {tableName} ORDER BY IIMAGE24_FT";
        int rowCount = 0;

        using (var command = new OracleCommand(selectDataSql, oracleConnection))
        using (var reader = command.ExecuteReader())
        {
            var loader = PreparePgSqlLoader(pgConnection, tableName, columns);
            while (reader.Read())
            {
                foreach (var column in columns)
                {
                    var value = reader[column.Name];
                    /*
                    if (column.Name.ToUpper() == "OIIMAGE24_FT")
                    {
                        value = 0; // Set OIIMAGE24_FT to 0
                    }
                    */
                    loader.SetValue(column.Name, value);
                }
                loader.NextRow();
                rowCount++;

                if (rowCount % 10000 == 0)
                {
                    Console.WriteLine($"Processed {rowCount} rows");
                }
            }
            loader.Dispose();
        }
        return rowCount;
    }

    private void UpdateRDCMMIMAGBlob(string tableName, double? startValue)
    {
        try
        {
            var selectBlobSql = $"SELECT IIMAGE24_FT, CRFIMAGE_BL FROM {tableName} WHERE IIMAGE24_FT > :startValue order by IIMAGE24_FT";
            //var updateSql = $"UPDATE {tableName} SET CRFIMAGE_BL = @blob, OIIMAGE24_FT = @status WHERE IIMAGE24_FT = @guid";
            var updateSql = $"UPDATE {tableName} SET CRFIMAGE_BL = @blob WHERE IIMAGE24_FT = @guid";
            int rowCount = 0;
            using (var selectCommand = new OracleCommand(selectBlobSql, oracleConnection))
            {
                if (startValue.HasValue)
                    selectCommand.Parameters.Add(":startValue", OracleDbType.Double).Value = startValue;
                using (var reader = selectCommand.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        bool isCanExec = true;
                        var guid = reader.GetString(0);
                        object? blob = null;
                        // Handle BLOB data
                        try
                        {
                            blob = reader.IsDBNull(1) ? null : (byte[])reader.GetValue(1);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error {guid}: {ex.Message}");
                            isCanExec = false;
                        }                        

                        if (isCanExec)
                        {
                            using (var updateCommand = new PgSqlCommand(updateSql, pgConnection))
                            {
                                updateCommand.Parameters.AddWithValue("@guid", guid);
                                updateCommand.Parameters.AddWithValue("@blob", blob ?? (object)DBNull.Value);
                                //updateCommand.Parameters.AddWithValue("@status", status);
                                updateCommand.ExecuteNonQuery();
                            }
                        }
                        rowCount++;
                        if (rowCount % 10000 == 0)
                            Console.WriteLine($"Processed {rowCount} image rows");
                    }
                }
            }
        }

        catch (Exception ex)
        {
            Console.WriteLine($"Error in UpdateRDCMMIMAGBlob: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw;
        }
    }


    public int TransferRDCMMIMAG(string tableName, int numberOfGroups = 10)
    {
        Console.WriteLine($"Starting specialized transfer for table: {tableName}");
        var startTime = DateTime.Now;
        int processedRows = 0;

        try
        {
            // Existing group transfer logic
            var iimage24FtValues = GetOrderedIIMAGE24FTValues(tableName);
            int totalRows = iimage24FtValues.Count;
            // Get the last transferred IIMAGE24_FT value from PostgreSQL            
            double lastTransferredValue = GetLastTransferredIIMAGE24FTValue(tableName);
            int startIndex = iimage24FtValues.FindIndex(v => v > lastTransferredValue);
            int startRow = (int)GetTableRowCount(pgConnection, tableName);

            // Step 1: Transfer data without CRFIMAGE_BL
            if (numberOfGroups == -2)
            {
                lastTransferredValue = GetLastUpdatedIIMAGE24FTValue(tableName);

                // Step 1: Transfer data without CRFIMAGE_BL
                if(lastTransferredValue == 0)
                    processedRows = TransferRDCMMIMAGWithoutBlob(tableName);

                // Step 2: Update CRFIMAGE_BL and OIIMAGE24_FT
                UpdateRDCMMIMAGBlob(tableName, lastTransferredValue);
            }
            else if (numberOfGroups == -1)
            {
                // Transfer remaining rows
                Console.WriteLine("Transferring rows.");
                int totalProcessedRows = 0;
                if (lastTransferredValue > 0)
                {
                    processedRows = TransferRDCMMIMAGGroupDirect(tableName, startValue: lastTransferredValue, endValue: null);
                }
                else
                {
                    startIndex = 0;
                    processedRows = TransferRDCMMIMAGGroupDirect(tableName, startValue: null, endValue: null, totalRows: totalRows);
                }

                Console.WriteLine($"Transferred {totalProcessedRows} row(s) in total.");
            }
            else if (numberOfGroups == 0)
            {
                // Transfer remaining rows
                Console.WriteLine("Transferring remaining rows.");
                int totalProcessedRows = 0;

                for (int i = startIndex; i < totalRows; i++)
                {
                    double startValue = iimage24FtValues[i];
                    processedRows = TransferRDCMMIMAGGroupDirect(tableName, startValue: startValue, endValue: null, startRow: startRow, totalRows: totalRows);
                    totalProcessedRows += processedRows;
                    Console.WriteLine($"Transferred row {i + 1}/{totalRows}");
                }
                Console.WriteLine($"Transferred {totalProcessedRows} row(s) in total.");
            }
            else if (numberOfGroups == 1)
            {
                // Transfer remaining rows
                Console.WriteLine("Transferring remaining rows.");
                int totalProcessedRows = 0;
                double startValue = lastTransferredValue;
                processedRows = TransferRDCMMIMAGGroupDirect(tableName, startValue: startValue, endValue: null, startRow: startRow, totalRows: totalRows);
                totalProcessedRows += processedRows;
                Console.WriteLine($"Transferred {totalProcessedRows} row(s) in total.");
            }
            else
            {

                if (startIndex == -1)
                {
                    Console.WriteLine("All rows have been transferred. Exiting.");
                    return 0;
                }

                Console.WriteLine($"Continuing transfer from IIMAGE24_FT value: {iimage24FtValues[startIndex]}");

                int remainingRows = totalRows - startIndex;
                int rowsPerGroup = (remainingRows + numberOfGroups - 1) / numberOfGroups; // Ceiling division to ensure all rows are covered
                //int rowsPerGroup = remainingRows / numberOfGroups; // Ceiling division to ensure all rows are covered

                Console.WriteLine($"Remaining rows: {remainingRows}, Rows per group: {rowsPerGroup}");

                // Step 2: Process each group
                for (int i = 0; i < numberOfGroups; i++)
                {
                    int groupStartIndex = startIndex + (i * rowsPerGroup);
                    int groupEndIndex = Math.Min(startIndex + ((i + 1) * rowsPerGroup), totalRows) - 1;

                    if (groupStartIndex >= totalRows)
                    {
                        Console.WriteLine("All rows have been transferred. Exiting.");
                        break;
                    }

                    double startValue = iimage24FtValues[groupStartIndex];
                    double endValue = iimage24FtValues[groupEndIndex];

                    int rowsInGroup = groupEndIndex - groupStartIndex + 1;
                    Console.WriteLine($"Processing group {i + 1}/{numberOfGroups}, IIMAGE24_FT range: {startValue} to {endValue}, Rows in group: {rowsInGroup}");

                    // Get the current row count in PostgreSQL
                    long beforeCount = GetTableRowCount(pgConnection, tableName);

                    // Transfer data for this group using the new direct insert method
                    processedRows = TransferRDCMMIMAGGroupDirect(tableName, startValue, endValue, groupStartIndex + 1, groupEndIndex + 1, totalRows);

                    // Get the new row count in PostgreSQL
                    long afterCount = GetTableRowCount(pgConnection, tableName);

                    // Calculate the number of rows inserted
                    long insertedRows = afterCount - beforeCount;

                    Console.WriteLine($"Completed group {i + 1}/{numberOfGroups}, Processed {processedRows} rows, Inserted {insertedRows} rows");

                    // Check if any rows were inserted
                    if (insertedRows == 0)
                    {
                        Console.WriteLine("WARNING: No new rows were inserted in this group.");
                        Console.WriteLine($"Rows processed: {processedRows}, Rows in PostgreSQL before: {beforeCount}, after: {afterCount}");
                        Console.WriteLine("Debugging information:");
                        Console.WriteLine($"Start IIMAGE24_FT: {startValue}, End IIMAGE24_FT: {endValue}");
                        Console.WriteLine($"Rows in Oracle for this range: {GetOracleRowCountInRange(tableName, startValue, endValue)}");

                        // You might want to add more debugging information here

                        Console.WriteLine("Continuing to next group...");
                    }
                }
            }
            // Update log entry with process time and status
            var processTime = DateTime.Now - startTime;
            UpdateLogEntry(tableName, DateTime.Now, totalRows, GetTableRowCount(pgConnection, tableName), processTime, "Completed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in TransferRDCMMIMAG for table {tableName}: {ex.Message}");
            var processTime = DateTime.Now - startTime;
            UpdateLogEntry(tableName, DateTime.Now, 0, 0, processTime, $"Error: {ex.Message}");
        }

        var totalProcessTime = DateTime.Now - startTime;
        Console.WriteLine($"Completed specialized transfer for table: {tableName}");
        Console.WriteLine($"Total process time: {FormatTimeSpan(totalProcessTime)}");
        return processedRows;
    }

    private double GetLastTransferredIIMAGE24FTValue(string tableName)
    {
        string sql = $"SELECT COALESCE(MAX(IIMAGE24_FT), -1) FROM {tableName}";
        return pgConnection.ExecuteScalar<double>(sql);
    }

     private double GetLastUpdatedIIMAGE24FTValue(string tableName)
    {
        string sql = $"SELECT COALESCE(MAX(IIMAGE24_FT), 0) FROM {tableName} WHERE IIMAGE24_FT > 0 AND OIIMAGE24_FT > 0";
        return pgConnection.ExecuteScalar<double>(sql);
    }

    public int TransferRDCMMIMAGGroupDirect(string tableName, double? startValue = null, double? endValue = null, int? startRow = null, int? endRow = null, int totalRows = 0)
    {

        string selectSql;
        object parameters;
        int processedRows = 0;
        int liError = 0;

        if (startValue.HasValue && endValue.HasValue)
        {
            // Existing group transfer logic
            selectSql = $@"SELECT * FROM {tableName} WHERE IIMAGE24_FT >= :startValue AND IIMAGE24_FT <= :endValue ORDER BY IIMAGE24_FT";
            parameters = new { startValue = startValue.Value, endValue = endValue.Value };
        }
        else if (startValue.HasValue)
        {
            // Single row transfer logic
            selectSql = $@"SELECT * FROM {tableName} WHERE IIMAGE24_FT > :value ORDER BY IIMAGE24_FT";
            parameters = new { value = startValue.Value };
        }
        else
        {
            selectSql = $@"SELECT * FROM {tableName} where IIMAGE24_FT > 0 order by IIMAGE24_FT";
            //throw new ArgumentException("Invalid parameters for transfer");
        }

        using (var command = new OracleCommand(selectSql, oracleConnection))
        {
            if (startValue.HasValue && endValue.HasValue)
            {
                command.Parameters.Add(":startValue", OracleDbType.Double).Value = startValue;
                command.Parameters.Add(":endValue", OracleDbType.Double).Value = endValue;
            }
            else if (startValue.HasValue)
                command.Parameters.Add(":value", OracleDbType.Double).Value = startValue;

            using (var reader = command.ExecuteReader())
            {
                var columns = GetOracleColumns(oracleConnection, tableName);
                var insertSql = BuildInsertSql(tableName, columns);

                int currentRow = 0;
                if (startRow.HasValue)
                    currentRow = (int)startRow + 1;
                while (reader.Read())
                {
                    try
                    {
                        using (var pgCommand = new PgSqlCommand(insertSql, pgConnection))
                        {
                            double iimage24Ft = Convert.ToDouble(reader["IIMAGE24_FT"]);
                            bool isCanExec = true;
                            if (iimage24Ft > 0)
                            {
                                foreach (var column in columns)
                                {
                                    object value;
                                    if (column.Name.ToUpper() == "CRFIMAGE_BL")
                                    {
                                        // Handle BLOB data
                                        try
                                        {
                                            value = reader[column.Name];
                                            /*
                                            if (value != DBNull.Value)                                        
                                            {
                                                value = (byte[])value;
                                            }
                                            */
                                        }
                                        catch (Exception ex)
                                        {
                                            value = null!;
                                            Console.WriteLine($"Error {iimage24Ft}  processing row {currentRow}: {ex.Message}");
                                            isCanExec = false;
                                            liError++;
                                        }
                                    }
                                    else
                                        value = reader[column.Name];

                                    pgCommand.Parameters.AddWithValue($"@{column.Name}", value ?? DBNull.Value);
                                }

                                if (isCanExec)
                                {
                                    int rowsAffected = pgCommand.ExecuteNonQuery();
                                    if (rowsAffected > 0)
                                    {
                                        processedRows++;
                                    }
                                    else
                                    {
                                        Console.WriteLine($"Warning: Row {currentRow} was not inserted.");
                                        LogRowDetails(reader, columns);
                                    }
                                    // Show progress for each IIMAGE24_FT value    
                                    if (currentRow % 1000 == 0)
                                        Console.WriteLine($"Processing IIMAGE24_FT: {iimage24Ft} (Row {currentRow}/{totalRows})");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing row {currentRow}: {ex.Message}");
                        LogRowDetails(reader, columns);
                    }

                    currentRow++;
                }
            }
        }
        return processedRows;
    }


    private void LogRowDetails(OracleDataReader reader, IEnumerable<ColumnInfo> columns)
    {
        Console.WriteLine("Row details:");
        foreach (var column in columns)
        {
            var value = reader[column.Name];
            Console.WriteLine($"{column.Name}: {value ?? "NULL"}");
        }
    }

    private long GetOracleRowCountInRange(string tableName, double startValue, double endValue)
    {
        var sql = $"SELECT COUNT(*) FROM {tableName} WHERE IIMAGE24_FT > :startValue AND IIMAGE24_FT <= :endValue";
        using (var command = new OracleCommand(sql, oracleConnection))
        {
            command.Parameters.Add(":startValue", OracleDbType.Double).Value = startValue;
            command.Parameters.Add(":endValue", OracleDbType.Double).Value = endValue;
            return Convert.ToInt64(command.ExecuteScalar());
        }
    }

    private readonly string[] rowByRowTables = { "RDCMMIMAG" };


    public void TransferData(string tableName)
    {
        Console.WriteLine($"Starting data transfer for table: {tableName}");
        var startTime = DateTime.Now;

        var oracleUpdateTime = GetOracleTableUpdateTime(tableName);
        var columns = FetchOracleColumns(oracleConnection, tableName);
        long oracleRowCount = GetTableRowCount(oracleConnection, tableName);
        long postgresRowCount = 0; // Initialize PostgreSQL row count

        string status = "OK";

        try
        {
            // Check if we should truncate the table
            bool shouldTruncate = ShouldTruncateTable(tableName);

            if (shouldTruncate)
            {
                Console.WriteLine($"Truncating table: {tableName}");
                TruncatePostgresTable(tableName);
            }
            else
            {
                Console.WriteLine($"Skipping truncation for table: {tableName}");
            }

            var selectFields = columns.Select(c => DecodeColumn(c.Name));
            var selectDataSql = $"SELECT {string.Join(", ", selectFields)} FROM {tableName}";

            if (tableName.Equals("RDCMMIMAG", StringComparison.OrdinalIgnoreCase))
            {
                postgresRowCount = TransferRDCMMIMAG(tableName, -2);
            }
            else if (rowByRowTables.Contains(tableName, StringComparer.OrdinalIgnoreCase))
            {
                var selectFieldsList = string.Join(", ", selectFields);
                postgresRowCount = TransferRowByRow(tableName, columns, selectFieldsList, oracleRowCount);
            }
            else
            {
                postgresRowCount = TransferBulk(tableName, columns, selectDataSql, oracleRowCount);
            }

            // Update log entry with process time and status
            var processTime = DateTime.Now - startTime;
            UpdateLogEntry(tableName, oracleUpdateTime, oracleRowCount, postgresRowCount, processTime, status);
        }
        catch (Exception ex)
        {
            status = "Error";
            HandleTransferError(tableName, ex);

            // Update log entry with error status
            var processTime = DateTime.Now - startTime;
            UpdateLogEntry(tableName, oracleUpdateTime, oracleRowCount, postgresRowCount, processTime, $"Error: {ex.Message}");
        }

        var totalProcessTime = DateTime.Now - startTime;
        Console.WriteLine($"Completed data transfer for table: {tableName}");
        Console.WriteLine($"Process time: {FormatTimeSpan(totalProcessTime)}");
        Console.WriteLine($"Status: {status}");
    }

    private bool ShouldTruncateTable(string tableName)
    {
        // Add logic here to determine if the table should be truncated
        // For example, we'll skip truncation for RDCMMIMAG and any other special tables
        if (tableName.Equals("RDCMMIMAG", StringComparison.OrdinalIgnoreCase))
        {
            return false;
        }

        // Add more conditions here if needed for other tables

        // By default, return true to truncate other tables
        return true;
    }

    private void TruncatePostgresTable(string tableName)
    {
        try
        {
            var truncateSql = $"TRUNCATE TABLE {tableName} RESTART IDENTITY CASCADE;";
            pgConnection.Execute(truncateSql);
            Console.WriteLine($"Table {tableName} truncated successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error truncating table {tableName}: {ex.Message}");
            throw;
        }
    }

    private long TransferRowByRow(string tableName, IEnumerable<ColumnInfo> columns, string selectFieldsList, long totalRows)
    {
        long existingRows = GetTableRowCount(pgConnection, tableName);
        long batchSize = 10000; // Adjust this value based on your needs
        long batches = (totalRows - existingRows + batchSize - 1) / batchSize;

        // Check if the encoded query works
        var checkSql = $"SELECT {selectFieldsList} FROM {tableName} WHERE ROWNUM = 1";
        try
        {
            var result = oracleConnection.Query(checkSql).ToList();
            if (!result.Any())
            {
                throw new Exception("No rows returned from check query");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in check query for table {tableName}: {ex.Message}");
            Console.WriteLine("Falling back to original column names...");
            selectFieldsList = string.Join(", ", columns.Select(c => c.Name));
        }

        for (long i = 0; i < batches; i++)
        {
            long startRow = existingRows + (i * batchSize) + 1;
            long endRow = Math.Min(existingRows + ((i + 1) * batchSize), totalRows);

            var partialSelectSql = BuildPartialSelectSql(tableName, selectFieldsList, startRow, endRow);

            Console.WriteLine($"Transferring data for {tableName}, rows {startRow} to {endRow} of {totalRows}");
            try
            {
                TransferLargeTable(tableName, columns, partialSelectSql, startRow - 1, endRow, 1);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error transferring rows {startRow} to {endRow} for table {tableName}: {ex.Message}");
                // Optionally, you can choose to continue with the next batch or throw to stop the entire process
            }
        }

        return GetTableRowCount(pgConnection, tableName);
    }

    private long TransferBulk(string tableName, IEnumerable<ColumnInfo> columns, string selectDataSql, long totalRows)
    {
        long transferredRows = 0;
        try
        {
            var checkSql = $"{selectDataSql} WHERE ROWNUM = 1";
            var result = oracleConnection.Query(checkSql).ToList();
            transferredRows = TransferLargeTable(tableName, columns, selectDataSql, 0, totalRows, 10000);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in bulk transfer for table {tableName}: {ex.Message}");
            transferredRows = FallbackToOriginalColumnNames(tableName, columns, totalRows);
        }
        return transferredRows;
    }

    private long FallbackToOriginalColumnNames(string tableName, IEnumerable<ColumnInfo> columns, long totalRows)
    {
        var originalSelectDataSql = $"SELECT {string.Join(", ", columns.Select(c => c.Name))} FROM {tableName}";
        try
        {

            Console.WriteLine($"Attempting fallback transfer for table {tableName} using original column names");
            return TransferLargeTable(tableName, columns, originalSelectDataSql, 0, totalRows, 10000);
        }
        catch (Exception fallbackEx)
        {
            Console.WriteLine($"Fallback Query: {originalSelectDataSql}");
            Console.WriteLine($"Error in fallback TransferData for table {tableName}: {fallbackEx.Message}");
            Console.WriteLine($"Stack trace: {fallbackEx.StackTrace}");
            throw; // Re-throw the exception after logging
        }
    }

    private string BuildPartialSelectSql(string tableName, string selectFieldList, long startRow, long endRow)
    {
        return $@"
            SELECT {selectFieldList} FROM (
                SELECT a.*, ROWNUM rnum
                FROM (select * from {tableName}) a
                WHERE ROWNUM <= {endRow}
            )
            WHERE rnum >= {startRow}";
    }

    private void HandleTransferError(string tableName, Exception ex)
    {
        Console.WriteLine($"Error in TransferData for table {tableName}: {ex.Message}");
        Console.WriteLine($"Stack trace: {ex.StackTrace}");
        // Additional error handling logic can be added here
    }

    private long TransferLargeTable(string tableName, IEnumerable<ColumnInfo> columns, string selectDataSql, long startRow, long endRow, int ibatchSize)
    {
        long totalRows = endRow - startRow;
        int batchSize = ibatchSize;

        // Adjust batch size only if ibatchSize is 0 and total rows are over 1 million
        if (ibatchSize == 0)
        {
            batchSize = totalRows > 1000000 ? 100000 : 10000;
        }

        long processedRows = 0;

        Console.WriteLine($"Starting transfer for table {tableName} from row {startRow + 1} to {endRow}");
        Console.WriteLine($"Using batch size: {batchSize}");

        var columnNullableStatus = columns.ToDictionary(
            c => c.Name.ToUpper(),
            c => c.IsNullable
        );

        bool hasByteaColumn = columns.Any(c => c.Type.ToUpper() == "BLOB" || c.Type.ToUpper() == "LONG RAW");

        try
        {
            if (hasByteaColumn)
            {
                ProcessDataDirectlyWithBytea(tableName, selectDataSql, batchSize, columns, columnNullableStatus, ref processedRows, totalRows, startRow);
            }
            else
            {
                using (var loader = PreparePgSqlLoader(pgConnection, tableName, columns))
                {
                    try
                    {
                        ProcessDataDirectly(tableName, selectDataSql, batchSize, loader, columns, columnNullableStatus, ref processedRows, totalRows, startRow);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error reading data from table {tableName}: {ex.Message}");
                        Console.WriteLine($"Stack trace: {ex.StackTrace}");
                        LogOracleColumnNames(tableName);
                    }
                }
            }

            // Compare processedRows with totalRows to check if all rows were transferred
            if (processedRows == totalRows)
            {
                Console.WriteLine($"Table {tableName}: All {totalRows} rows transferred successfully.");
            }
            else if (processedRows < totalRows)
            {
                Console.WriteLine($"Table {tableName}: Warning - Only {processedRows} out of {totalRows} rows transferred.");
            }
            else
            {
                Console.WriteLine($"Table {tableName}: Warning - More rows transferred ({processedRows}) than expected ({totalRows}).");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in TransferLargeTable for table {tableName}: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw; // Re-throw the exception after logging
        }

        return processedRows;
    }

    private void LogOracleColumnNames(string tableName)
    {
        var columnQuery = $"SELECT column_name FROM all_tab_columns WHERE table_name = '{tableName.ToUpper()}'";
        var columnNames = oracleConnection.Query<string>(columnQuery).ToList();
        Console.WriteLine($"Columns in Oracle table {tableName}:");
        foreach (var columnName in columnNames)
        {
            Console.WriteLine(columnName);
        }
    }

    private Dictionary<string, int> columnOrdinals;

    private void InitializeColumnOrdinals(OracleDataReader reader)
    {
        columnOrdinals = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
        for (int i = 0; i < reader.FieldCount; i++)
        {
            string columnName = reader.GetName(i);
            if (!string.IsNullOrEmpty(columnName))
            {
                columnOrdinals[columnName] = i;
            }
            else
            {
                Console.WriteLine($"Warning: Empty column name found at index {i}");
            }
        }
    }

    private void ProcessDataDirectly(string tableName, string selectDataSql, int batchSize, PgSqlLoader loader,
                                     IEnumerable<ColumnInfo> columns, Dictionary<string, bool> columnNullableStatus,
                                     ref long processedRows, long totalRows, long startRow)
    {
        using (var command = new OracleCommand(selectDataSql, oracleConnection))
        using (var reader = command.ExecuteReader())
        {
            InitializeColumnOrdinals(reader);
            int rowsInBatch = 0;

            while (reader.Read())
            {
                try
                {
                    InsertRow(loader, tableName, columns, columnNullableStatus, reader);
                    rowsInBatch++;
                    processedRows++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing row in table {tableName}: {ex.Message}");
                    // Optionally log more details about the problematic row
                }

                if (rowsInBatch >= batchSize)
                {
                    Console.WriteLine($"Table {tableName}: Processed {processedRows:N0} / {totalRows:N0} rows (Current row: {startRow + processedRows:N0})");
                    rowsInBatch = 0;
                }
            }

            // Show final progress
            if (rowsInBatch > 0 || processedRows == totalRows)
            {
                Console.WriteLine($"Table {tableName}: Processed {processedRows:N0} / {totalRows:N0} rows (Final row: {startRow + processedRows:N0})");
            }
        }
    }

    private void InsertRow(PgSqlLoader loader, string tableName, IEnumerable<ColumnInfo> columns,
                           Dictionary<string, bool> columnNullableStatus, OracleDataReader reader)
    {
        try
        {
            foreach (PgSqlLoaderColumn col in loader.Columns)
            {
                if (!columnOrdinals.TryGetValue(col.Name, out int ordinal))
                {
                    Console.WriteLine($"Warning: Column {col.Name} not found in Oracle data for table {tableName}");
                    continue;
                }

                object laValue = reader.IsDBNull(ordinal) ? DBNull.Value : reader.GetValue(ordinal);

                bool isNullable = columnNullableStatus.TryGetValue(col.Name.ToUpper(), out bool nullable) && nullable;

                if (!isNullable && Convert.IsDBNull(laValue))
                {
                    if (col.PgSqlType == PgSqlType.Numeric)
                        laValue = 0;
                    else if (col.PgSqlType == PgSqlType.TimeStamp)
                        laValue = DateTime.MinValue;
                    else
                        laValue = " ";
                }
                else if (col.PgSqlType == PgSqlType.TimeStamp && laValue is string)
                {
                    laValue = TryParse(laValue, out int liType) ?? (isNullable ? (object)DBNull.Value : DateTime.MinValue);
                    // Generate update script for date columns
                    if (liType > 1)
                    {
                        string guid = Convert.ToString(reader["IGUID32_FT"])!;
                        GenerateDateUpdateScript(tableName, col.Name, guid, (DateTime?)laValue, liType);
                    }
                }
                loader.SetValue(col.Name, laValue);
            }
            loader.NextRow();

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in InsertRow for table {tableName}: {ex.Message}");
            Console.WriteLine($"Columns in PostgreSQL: {string.Join(", ", loader.Columns.Cast<PgSqlLoaderColumn>().Select(c => c.Name))}");
            Console.WriteLine($"Columns in Oracle: {string.Join(", ", columnOrdinals.Keys)}");
            throw;
        }
    }

    private Dictionary<string, StreamWriter> tableWriters = new Dictionary<string, StreamWriter>();
    private Dictionary<string, int> tableRowCounts = new Dictionary<string, int>();

    private void GenerateDateUpdateScript(string tableName, string columnName, string guid, DateTime? dateValue, int riType)
    {
        string fileName = $"{tableName}.sql";

        if (!tableWriters.TryGetValue(tableName, out StreamWriter? writer))
        {
            writer = new StreamWriter(fileName, true);
            tableWriters[tableName] = writer;
            tableRowCounts[tableName] = 0;
        }

        if (riType > 0 && dateValue.HasValue)
        {
            string dateString = dateValue.Value.ToString("yyyyMMdd HHmmss");
            string formattedDate = string.Empty;

            if (riType == 2) // Other formats parsed by DateTime.TryParse
            {
                formattedDate = dateString;
            }

            // Only generate UPDATE statement if the formatted date is not empty and guid is not null or empty
            if (!string.IsNullOrEmpty(formattedDate) && !string.IsNullOrEmpty(guid))
            {
                string updateSql = $"/* {guid} {dateValue} */\n" +
                                    $"UPDATE {tableName} SET {columnName} = Encrypt(Rpad('{formattedDate}',24),'56BitKey') " +
                                    $"WHERE IGUID32_FT = '{guid}';\n";

                writer.WriteLine(updateSql);
                tableRowCounts[tableName]++;

                // Add COMMIT after every 10 rows
                if (tableRowCounts[tableName] % 10 == 0)
                {
                    writer.WriteLine("COMMIT;\n");
                }

                Console.WriteLine($"Generated update script for table {tableName}, column {columnName}, GUID {guid}, DateString {dateValue}");
            }
            else
            {
                Console.WriteLine($"Skipped update script generation for table {tableName}, column {columnName}. FormattedDate: '{formattedDate}', GUID: '{guid}'");
            }
        }
        else
        {
            Console.WriteLine($"Skipped update script generation for table {tableName}, column {columnName}. Invalid riType or dateValue");
        }
    }

    // Add this method to close all StreamWriters
    public void CloseAllWriters()
    {
        foreach (var entry in tableWriters)
        {
            var tableName = entry.Key;
            var writer = entry.Value;

            // Add final COMMIT if there are any pending rows
            if (tableRowCounts[tableName] % 10 != 0)
            {
                writer.WriteLine("COMMIT;\n");
            }

            writer.Dispose();
        }
        tableWriters.Clear();
        tableRowCounts.Clear();
    }

    private void FetchAndInsertDataRowByRow(OracleConnection oracleConnection, PgSqlConnection pgConnection, string tableName, IEnumerable<ColumnInfo> columns, string selectDataSql)
    {
        var guidQuery = $"SELECT IGUID32_FT FROM {tableName}";
        var guids = oracleConnection.Query<string>(guidQuery).ToList();

        int exportedRows = 0;
        int importedRows = 0;

        // Prepare the PgSqlLoader
        PgSqlLoader loader = PreparePgSqlLoader(pgConnection, tableName, columns);

        foreach (var guid in guids)
        {
            var guidFilteredSql = $"{selectDataSql} WHERE IGUID32_FT = :guid";
            var rows = oracleConnection.Query(guidFilteredSql, new { guid }).ToList();

            exportedRows += rows.Count;
            importedRows += InsertRowToPostgres(loader, tableName, columns, rows);
        }

        loader.Dispose();
        Console.WriteLine($"Table {tableName}: Exported {exportedRows} rows, Imported {importedRows} rows");
    }

    private PgSqlLoader PreparePgSqlLoader(PgSqlConnection pgConnection, string tableName, IEnumerable<ColumnInfo> columns)
    {
        PgSqlLoader loader = new PgSqlLoader();
        loader.TableName = tableName.ToLower();
        loader.Connection = pgConnection;
        loader.CreateColumns();
        loader.Open();
        return loader;
    }

    private int InsertRowToPostgres(PgSqlLoader loader, string tableName, IEnumerable<ColumnInfo> columns, List<dynamic> data)
    {
        return BulkInsertDataToPostgres(loader, tableName, columns, data);
    }

    private IEnumerable<ColumnInfo> FetchOracleColumns(OracleConnection oracleConnection, string tableName)
    {
        return GetOracleColumns(oracleConnection, tableName);
    }

    private List<dynamic> FetchOracleData(OracleConnection oracleConnection, string tableName, IEnumerable<ColumnInfo> columns, string selectDataSql)
    {
        try
        {
            var checkSql = $"{selectDataSql} WHERE ROWNUM = 1";
            var result = oracleConnection.Query(checkSql).ToList();
            if (result.Any())
            {
                return oracleConnection.Query(selectDataSql).ToList();
            }
            return new List<dynamic>();
        }
        catch (Exception ex)
        {
            // If we encounter ORA-03106, switch to using IDataReader for row-by-row processing
            if (ex.Message.Contains("ORA-03106"))
            {
                Console.WriteLine($"Encountered ORA-03106 error. Switching to row-by-row processing for table: {tableName}");
                return null!;
            }
            Console.WriteLine($"Error executing Oracle query: {ex.Message}");
            // If the decoded query fails, fall back to the original column names
            var originalSelectDataSql = $"SELECT {string.Join(", ", columns.Select(c => c.Name))} FROM {tableName}";
            try
            {
                return oracleConnection.Query(originalSelectDataSql).ToList();
            }
            catch (Exception fallbackEx)
            {
                Console.WriteLine($"Error executing fallback Oracle query: {fallbackEx.Message}");
                Console.WriteLine($"Fallback Query: {originalSelectDataSql}");
                throw; // Re-throw the exception after logging
            }
        }
    }

    private List<dynamic> FetchOracleDataRowByRow(OracleConnection oracleConnection, string selectDataSql, string tableName)
    {

        var data = new List<dynamic>();
        using (var command = new OracleCommand(selectDataSql, oracleConnection))
        using (var reader = command.ExecuteReader())
        {
            while (reader!.Read())
            {
                try
                {
                    var row = new System.Dynamic.ExpandoObject() as IDictionary<string, object>;
                    for (int i = 0; i < reader?.FieldCount; i++)
                    {
                        try
                        {
                            row[reader.GetName(i)] = reader.IsDBNull(i) ? DBNull.Value : reader.GetValue(i);
                        }
                        catch (Exception fieldEx)
                        {
                            Console.WriteLine($"Error reading field {reader.GetName(i)} in table {tableName}: {fieldEx.Message}");
                            row[reader.GetName(i)] = DBNull.Value;
                        }
                    }
                    data.Add(row);
                }
                catch (Exception rowEx)
                {
                    Console.WriteLine($"Error reading row in table {tableName}: {rowEx.Message}");
                }
            }
        }
        return data;
    }

    private string DecodeColumn(string columnName)
    {
        string suffix = columnName.ToUpper().LastIndexOf('_') != -1 ? "_" + columnName.ToUpper().Split('_').Last() : "";

        switch (suffix)
        {
            case "_DT":
                //return $"TO_DATE(RTrim(Decrypt({columnName},'56BitKey')),'YYYY/MM/DD HH24:MI:SS') AS {columnName}";
                return $"RTrim(Decrypt({columnName},'56BitKey')) AS {columnName}";
            case "_NM":
                return $"TO_NUMBER(RTrim(Decrypt({columnName},'56BitKey'))) AS {columnName}";
            case "_OR":
            case "_FT":
            case "_BL":
            case "":
                return columnName;
            default:
                return $"RTrim(Decrypt({columnName},'56BitKey')) AS {columnName}";
        }
    }

    private DateTime? TryParse(object raDate, out int riType)
    {
        string? lsDate = Convert.ToString(raDate);
        riType = -1;
        if (string.IsNullOrEmpty(lsDate))
            return null;

        if (DateTime.TryParseExact(lsDate, "yyyyMMdd HHmmss", null,
                                   System.Globalization.DateTimeStyles.None,
                                   out DateTime laDate))
        {
            riType = 0;
            return laDate;
        }
        if (DateTime.TryParseExact(lsDate, "yyyyMMdd", null,
                                   System.Globalization.DateTimeStyles.None,
                                   out laDate))
        {
            riType = 1;
            return laDate;
        }
        if (DateTime.TryParse(lsDate, out laDate))
        {
            // Oracle returns dates in the format "YYYY/MM/DD HH:MM:SS"
            if (Convert.ToString(raDate)?.Length > 10)
                riType = 2;
            else
                riType = 1;
            return laDate;
        }
        return null;
    }

    private int BulkInsertDataToPostgres(PgSqlLoader loader, string tableName, IEnumerable<ColumnInfo> columns, List<dynamic> data)
    {
        int rowsTransferred = 0;

        try
        {
            foreach (var row in data)
            {
                foreach (PgSqlLoaderColumn col in loader.Columns)
                {
                    object laValue = ((IDictionary<string, object>)row)[col.Name.ToUpper()];
                    var columnInfo = columns.FirstOrDefault(c => c.Name.Equals(col.Name, StringComparison.OrdinalIgnoreCase));

                    if (columnInfo != null && !columnInfo.IsNullable && Convert.IsDBNull(laValue))
                    {
                        if (col.PgSqlType == PgSqlType.Numeric)
                            laValue = 0;
                        else if (col.PgSqlType == PgSqlType.Date)
                            laValue = DateTime.MinValue;
                        else
                            laValue = " ";
                    }
                    else if (col.PgSqlType == PgSqlType.Date && laValue is string)
                    {
                        laValue = TryParse(laValue, out int liType) ?? (columnInfo?.IsNullable == true ? (object)DBNull.Value : DateTime.MinValue);
                    }
                    loader.SetValue(col.Name, laValue);
                }
                loader.NextRow();
                rowsTransferred++;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error inserting rows into {tableName}: {ex.Message}");
        }

        return rowsTransferred;
    }

    private void InsertDataToPostgres(PgSqlConnection pgConnection, string tableName, List<string> columns, List<dynamic> data)
    {
        var insertSql = $"INSERT INTO {tableName} ({string.Join(", ", columns)}) VALUES ({string.Join(", ", columns.Select(c => $":{c}"))})";

        using (var transaction = pgConnection.BeginTransaction())
        {
            try
            {
                pgConnection.Execute(insertSql, data, transaction);
                transaction.Commit();
                Console.WriteLine($"Data transferred successfully for table: {tableName}");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                Console.WriteLine($"Error transferring data for table {tableName}: {ex.Message}");
            }
        }
    }

    public void TransferDataSingleThreaded(List<string> tables)
    {
        foreach (var table in tables)
        {
            Console.WriteLine($"Starting data transfer for {table}");
            if (pgConnection.State == ConnectionState.Closed)
                pgConnection.Open();
            TransferData(table);
            Console.WriteLine($"Completed data transfer for {table}");
        }
        Console.WriteLine($"Data transfer completed for {tables.Count} tables.");
    }

    private void ProcessDataDirectlyWithBytea(string tableName, string selectDataSql, int batchSize,
                                          IEnumerable<ColumnInfo> columns, Dictionary<string, bool> columnNullableStatus,
                                          ref long processedRows, long totalRows, long startRow)
    {
        using (var command = new OracleCommand(selectDataSql, oracleConnection))
        using (var reader = command.ExecuteReader())
        {
            InitializeColumnOrdinals(reader);
            int rowsInBatch = 0;

            var insertSql = BuildInsertSql(tableName, columns);

            while (reader.Read())
            {
                try
                {
                    InsertRowWithBytea(tableName, columns, columnNullableStatus, reader, insertSql);
                    rowsInBatch++;
                    processedRows++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error inserting row in table {tableName}: {ex.Message}");
                }

                if (rowsInBatch >= batchSize)
                {
                    Console.WriteLine($"Table {tableName}: Processed {processedRows:N0} / {totalRows:N0} rows (Current row: {startRow + processedRows:N0})");
                    rowsInBatch = 0;
                }
            }

            // Show final progress
            if (rowsInBatch > 0 || processedRows == totalRows)
            {
                Console.WriteLine($"Table {tableName}: Processed {processedRows:N0} / {totalRows:N0} rows (Final row: {startRow + processedRows:N0})");
            }
        }
    }

    private string BuildInsertSql(string tableName, IEnumerable<ColumnInfo> columns)
    {
        var columnNames = string.Join(", ", columns.Select(c => c.Name));
        var paramNames = string.Join(", ", columns.Select(c => $"@{c.Name}"));
        return $"INSERT INTO {tableName} ({columnNames}) VALUES ({paramNames})";
    }

    private void InsertRowWithBytea(string tableName, IEnumerable<ColumnInfo> columns,
                                Dictionary<string, bool> columnNullableStatus, OracleDataReader reader, string insertSql)
    {
        using (var cmd = new PgSqlCommand(insertSql, pgConnection))
        {
            try
            {
                foreach (var column in columns)
                {
                    try
                    {
                        if (!columnOrdinals.TryGetValue(column.Name, out int ordinal))
                        {
                            Console.WriteLine($"Warning: Column {column.Name} not found in Oracle data for table {tableName}");
                            continue;
                        }

                        object value = reader.IsDBNull(ordinal) ? DBNull.Value : reader.GetValue(ordinal);

                        bool isNullable = columnNullableStatus.TryGetValue(column.Name.ToUpper(), out bool nullable) && nullable;

                        if (!isNullable && Convert.IsDBNull(value))
                        {
                            if (column.Type.ToUpper() == "NUMBER")
                                value = 0;
                            else if (column.Type.ToUpper() == "DATE")
                                value = DateTime.MinValue;
                            else
                                value = " ";
                        }
                        else if (column.Type.ToUpper() == "DATE" && value is string)
                        {
                            value = TryParse(value, out int liType) ?? (isNullable ? (object)DBNull.Value : DateTime.MinValue);
                        }
                        else if (column.Type.ToUpper() == "BLOB" || column.Type.ToUpper() == "LONG RAW")
                        {
                            if (!Convert.IsDBNull(value))
                            {
                                value = (byte[])value;
                            }
                        }

                        cmd.Parameters.AddWithValue($"@{column.Name}", value);
                    }
                    catch (Exception columnEx)
                    {
                        Console.WriteLine($"Error processing column {column.Name} for table {tableName}: {columnEx.Message}");
                    }
                }

                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error inserting row in table {tableName}: {ex.Message}");
                Console.WriteLine($"Columns in PostgreSQL: {string.Join(", ", columns.Select(c => c.Name))}");
                Console.WriteLine($"Columns in Oracle: {string.Join(", ", columnOrdinals.Keys)}");
                throw;
            }
        }
    }

    // Change this from private or protected to public
    public string FormatTimeSpan(TimeSpan timeSpan)
    {
        return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}.{timeSpan.Milliseconds:D3}";
    }

    private List<double> GetOrderedIIMAGE24FTValues(string tableName)
    {
        var sql = $"SELECT IIMAGE24_FT FROM {tableName} WHERE IIMAGE24_FT > 0 ORDER BY IIMAGE24_FT";
        return oracleConnection.Query<double>(sql).ToList();
    }
}

class Program
{
    static void Main(string[] args)
    {
        // Record the start time
        var startTime = DateTime.Now;

        try
        {
            // Parse command line arguments
            bool transferSchema = args.Contains("-s") || args.Contains("--schema");
            bool transferData = args.Contains("-d") || args.Contains("--data");
            string[] tablePrefixes = args.Where(arg => arg.StartsWith("-p=") || arg.StartsWith("--prefix="))
                                         .Select(arg => arg.Substring(arg.IndexOf('=') + 1))
                                         .ToArray();

            if (!transferSchema && !transferData)
            {
                Console.WriteLine("Please specify at least one of -s/--schema or -d/--data");
                Console.WriteLine("Usage examples:");
                Console.WriteLine("1. Convert schema and transfer data for tables starting with 'SYSMMROLE' and 'ANOTHER_PREFIX':");
                Console.WriteLine("   dotnet run -- -s -d -p=SYSMMROLE -p=ANOTHER_PREFIX");
                Console.WriteLine("2. Convert schema only for tables starting with 'USER_':");
                Console.WriteLine("   dotnet run -- -s -p=USER_");
                Console.WriteLine("3. Transfer data only for tables starting with 'EMPLOYEE' and 'DEPARTMENT':");
                Console.WriteLine("   dotnet run -- -d -p=EMPLOYEE -p=DEPARTMENT");
                Console.WriteLine("4. Convert schema and transfer data for all tables:");
                Console.WriteLine("   dotnet run -- -s -d -p=");
                Console.WriteLine("5. Convert schema and transfer data for all tables, ignoring those with matching row counts:");
                Console.WriteLine("   dotnet run -- -s -d -p= -i");
                Console.WriteLine("6. Convert schema and transfer data for all tables except 'TABLE1' and 'TABLE2':");
                Console.WriteLine("   dotnet run -- -s -d -p= -e=TABLE1,TABLE2");
                return;
            }

            if (tablePrefixes.Length == 0)
            {
                Console.WriteLine("Please specify at least one table prefix with -p= or --prefix=");
                Console.WriteLine("Example: -s -p=SYSMMROLE");
                return;
            }

            // Load configuration from appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            var oracleConnString = configuration.GetConnectionString("Oracle");
            var postgresConnString = configuration.GetConnectionString("PostgreSQL");

            if (string.IsNullOrEmpty(oracleConnString))
            {
                Console.WriteLine("Error: Oracle connection string not found in appsettings.json");
                return;
            }

            if (string.IsNullOrEmpty(postgresConnString))
            {
                Console.WriteLine("Error: PostgreSQL connection string not found in appsettings.json");
                return;
            }

            bool ignoreMatchingRowCounts = args.Contains("-i") || args.Contains("--ignore-matching");

            string[] excludedTables = args.Where(arg => arg.StartsWith("-e=") || arg.StartsWith("--exclude="))
                                          .SelectMany(arg => arg.Substring(arg.IndexOf('=') + 1).Split(','))
                                          .ToArray();

            var converter = new OracleToPostgresConverter(oracleConnString, postgresConnString, ignoreMatchingRowCounts, excludedTables.ToList());

            List<string> tablesToProcess = converter.GetTableList(tablePrefixes.ToList(), transferSchema, transferData);
            Console.WriteLine($"Number of tables to process: {tablesToProcess.Count}");

            if (tablesToProcess.Count == 0)
            {
                Console.WriteLine("No tables found matching the specified prefixes or needing processing. Exiting...");
                return;
            }

            if (transferSchema)
            {
                converter.ConvertSchema(tablesToProcess);
                Console.WriteLine($"Schema conversion completed for {tablesToProcess.Count} tables");
            }

            if (transferData || transferSchema)  // Transfer data if -d is specified or if schema was just converted
            {
                var overallStartTime = DateTime.Now;
                converter.TransferDataSingleThreaded(tablesToProcess);
                var overallProcessTime = DateTime.Now - overallStartTime;
                Console.WriteLine($"Total process time for all tables: {converter.FormatTimeSpan(overallProcessTime)}");
            }

            converter.Dispose();

            // Print completion message
            Console.WriteLine($"Operation completed successfully for tables starting with: {string.Join(", ", tablePrefixes)}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during operation: {ex.Message}");
        }
        finally
        {
            // Calculate and print the time consumed
            var endTime = DateTime.Now;
            var timeConsumed = endTime - startTime;
            Console.WriteLine($"Total time consumed: {timeConsumed.TotalSeconds:F2} seconds");
        }

        // Example usage:
        // Transfer schema only:
        // dotnet run -s -p=SYSMMROLE
        //
        // Transfer schema and data:
        // dotnet run -s -d -p=SYSMMROLE
        //
        // Transfer schema and data for multiple prefixes:
        // dotnet run -s -d -p=SYSMMROLE -p=ANOTHER_PREFIX
        //
        // Full argument names can also be used:
        // dotnet run --schema --data --prefix=SYSMMROLE --prefix=ANOTHER_PREFIX
    }
}